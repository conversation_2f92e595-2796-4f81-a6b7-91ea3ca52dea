package com.artemide.core;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.web.SecurityFilterChain;

import jakarta.servlet.DispatcherType;
import net.yadaframework.security.YadaSecurityConfig;

/**
 * Security configuration.
 * You only need to set the roles for the application paths.
 * Other configuration is set on YadaSecurityConfig
 * @see YadaSecurityConfig
 */
@Configuration
@EnableWebSecurity
public class SecurityConfig extends YadaSecurityConfig {
	
	public SecurityConfig() {
		loginUrl = "/my/area";
		loginUrlAjax = "/my/area";
	}

	@Bean
	public SecurityFilterChain securityConfigChain(HttpSecurity http) throws Exception {
		//	The mapping matches URLs using the following rules:
		//		? matches one character
		//		* matches zero or more characters
		//		** matches zero or more 'directories' in a path
		//	Patterns which end with /** (and have no other wildcards) are optimized by using a substring match
		http.securityMatcher("/**")
			.authorizeHttpRequests(authorize -> authorize
				.requestMatchers("/my/area").permitAll()
				.requestMatchers("/my/selections").permitAll()
				.requestMatchers("/my/registration").permitAll()
				// .requestMatchers(new AntPathRequestMatcher("/my/login")).permitAll()
				.requestMatchers("/my/reset").permitAll()
				.requestMatchers("/my/downloadPdf").permitAll()
				.requestMatchers("/my/*").authenticated()
				.requestMatchers("/my/collectionEdit/**").authenticated() // .hasRole("USER")
				.requestMatchers("/manager/dashboard").hasAnyRole("MANAGER", "SALESFORCE", "ADMIN", "DEVELOPER")
				.requestMatchers("/manager/bookings/**").hasAnyRole("MANAGER", "SALESFORCE")
				.requestMatchers("/manager/publications/**").hasAnyRole("MANAGER", "SALESFORCE")
				.requestMatchers("/manager/stores/**").hasAnyRole("MANAGER", "SALESFORCE")
				.requestMatchers("/manager/test/**").hasAnyRole("ADMIN", "DEVELOPER")
				.requestMatchers("/manager/**").hasAnyRole("MANAGER", "ADMIN")
				.requestMatchers("/**").permitAll()
				// Copied this from superclass because the super authorizeHttpRequests is probably ignored as it's done later.
				.dispatcherTypeMatchers(DispatcherType.FORWARD).permitAll() 
				)
			.formLogin(form -> form
					// Configure login page URL
					.loginPage("/my/area"))
			.logout(logout -> logout
					// Configure logout URL and success handler
					.logoutUrl("/logout")
					.logoutSuccessHandler(logoutSuccessHandler));
		super.configure(http);
		// super.failureHandler.setFailureUrlAjaxRequest("/my/area");
		// super.failureHandler.setFailureUrlNormalRequest("/my/area");
		super.successHandler.setDefaultTargetUrlNormalRequest("/my/area");
		super.successHandler.setDefaultTargetUrlAjaxRequest("/yadaLoginSuccess?targetUrl=/my/area");
		super.logoutSuccessHandler.setDefaultTargetUrl("/my/area"); // language path will be added in the handler

		return http.build();

    }
}
