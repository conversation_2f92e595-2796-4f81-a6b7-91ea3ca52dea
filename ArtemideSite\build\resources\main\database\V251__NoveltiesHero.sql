# Novelties hero gallery

create table CatalogConfig_YadaAttachedFile (CatalogConfig_id bigint not null, noveltiesHero_id bigint not null, primary key (CatalogConfig_id, noveltiesHero_id)) engine=InnoDB;

alter table CatalogConfig_YadaAttachedFile add constraint UK_7u4rm3pdw5jxnee190kocp1h3 unique (noveltiesHero_id);
alter table CatalogConfig_YadaAttachedFile add constraint FK8kp5n02gnpfldabi123lbpb3x foreign key (noveltiesHero_id) references YadaAttachedFile (id);
alter table CatalogConfig_YadaAttachedFile add constraint FKcbk6g91ca0n1d25k7ndpxb2j5 foreign key (CatalogConfig_id) references CatalogConfig (id);
