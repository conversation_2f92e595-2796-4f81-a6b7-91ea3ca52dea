/* **************** */
/* yadaEntitySorter */

.yadaEntitySorter img {
	width: 100%;
}

.yadaEntitySorter .commandBar {
	font-size: 1.8em;
}

.yadaEntitySorter .delete {
    float: right;
    padding: 5px 0 0 5px;
    color: red;
}
	
.yadaEntitySorter .commandBar form {
	display: inline;
}

.yadaEntitySorter .form-group {
	margin-bottom: 0;
}

.yadaEntitySorter .yadaImageControl {
	margin-top:50px;
}

/* *************** */
/* yadaImageSorter */

/* .yadaImageSorter {
	position: relative;
	max-width: 400px;
	display: block;
    margin: auto;
    padding: 10px;
} */

.yadaImageSorter img {
	width: 100%;
}

.yadaImageSorter .commandBar {
	font-size: 1.8em;
}

.yadaImageSorter .delete {
    float: right;
    padding-top: 5px;
    color: red;
}
	
.yadaImageSorter .commandBar form {
	display: inline;
}

.yadaImageSorter .form-group {
	margin-bottom: 0;
}