create table UserProfile_recentlyViewedProducts (UserProfile_id bigint not null, recentlyViewedProducts bigint) engine=InnoDB;
create table UserProfile_recentlyViewedSubfamilies (UserProfile_id bigint not null, recentlyViewedSubfamilies bigint) engine=InnoDB;
alter table UserProfile_recentlyViewedProducts add constraint FKgh9eb5dpukfl7qtyd678y0lkf foreign key (UserProfile_id) references YadaUserProfile (id);
alter table UserProfile_recentlyViewedSubfamilies add constraint FKefe63gnyaky81vm0vjpy6wv2i foreign key (UserProfile_id) references YadaUserProfile (id);
