<!--/*
A password input element.
DEPRECATED: usare text con type='password'

*/--><div class="form-group has-feedback" th:with="hasError=${#fields.hasErrors('__${fieldName}__')}" th:classappend="${hasError}? has-error">
	<label class="control-label" th:for="${#ids.next('fieldId')}" th:utext="${labelKey!=null?#messages.msg(labelKey):label}">Nome:</label>
	<div class="input-group">
		<input th:field="*{__${fieldName}__}" type="password" class="form-control has-feedback" required="required" th:id="${#ids.seq('fieldId')}" th:attr="aria-describedby=${#ids.next('fieldId')}"/>
		<span class="input-group-btn">
			<a class="btn btn-default yadaShowPassword" href="#" th:title="#{form.tooltip.password.show}"><span class="glyphicon glyphicon-eye-open"></span></a>
		</span>
	</div>
	<div th:each="err : ${#fields.errors('__${fieldName}__')}" class="alert alert-danger alert-dismissible yadaInputError" role="alert">
		<span th:text="${err}" th:id="${#ids.seq('fieldId')}">Error Text</span>
		<button type="button" class="close" data-bs-dismiss="alert"><span aria-hidden="true">&times;</span><span class="sr-only">Close</span></button>
	</div>
</div>    	
