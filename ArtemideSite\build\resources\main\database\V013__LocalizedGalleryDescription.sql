alter table GalleryDescription drop column description;

drop table if exists GalleryDescriptionText;

create table GalleryDescriptionText (id bigint not null auto_increment, localeCode varchar(5), value longtext, GalleryDescription_id bigint, primary key (id)) ENGINE=InnoDB;

alter table GalleryDescriptionText add index FKA7ADFDB7F9E3541D (GalleryDescription_id), add constraint FKA7ADFDB7F9E3541D foreign key (GalleryDescription_id) references GalleryDescription (id);