package com.artemide.common.repository;

import java.util.ArrayList;
import java.util.List;

import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;

import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Slice;
import org.springframework.data.domain.SliceImpl;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import com.artemide.common.persistence.entity.NewsJournal;
import com.artemide.common.persistence.entity.Project;
import com.yr.entity.Designer;
import com.yr.entity.Prodotto;
import com.yr.entity.Subfamily;

import net.yadaframework.components.YadaUtil;
import net.yadaframework.persistence.YadaSql;
import net.yadaframework.web.YadaPageRequest;
import net.yadaframework.web.YadaPageRows;

@Repository
@Transactional(readOnly = true)
public class SearchDao {

	@PersistenceContext EntityManager em;

	/**
     * @param searchString il testo da cercare
     * @param pageable
     * @return
     */
    public YadaPageRows<Subfamily> findProducts(String searchString, boolean preview, YadaPageRequest yadaPageRequest) {
    	// NOTA:
    	// In origine c'era solo la query di "findProductsNormally" che usava la fulltext search ma le parole piccole o con punteggiatura
    	// non venivano trovate: "a.24"
    	// Questa nuova query fa precedere la ricerca da "+*" e questo fa in modo che le parole piccole non siano ignorate.
    	// Inoltre usa il like normale nel where.
    	// Però alcune ricerche non vengono trovate in questo modo, per cui viene chiamata la query originale.
    	// Se questo metodo non funzionasse, si potrebbe fare prima la query "fulltext" originale e poi, in assenza di risultati, una query sql normale.
    	//
    	// Se cerco la O, aggiungo i doppi apici altrimenti trova troppe cose
    	if ("o".equalsIgnoreCase(searchString)) {
    		searchString = "\"" + searchString + "\"";
    	}
    	String locale = LocaleContextHolder.getLocale().toString();
		YadaSql yadaSql = YadaSql.instance().selectFrom(
			"select distinct q.* from ( "
		  + "select distinct s.*, sn.value "
		  // Queste due righe erano rimaste nella query ma non servono più:
		  // ", match(sn.value) against(CONCAT('+*',:search,'*') in boolean mode) as rn, "
		  // "match(d.name,d.surname) against(CONCAT('+*',:search,'*') in boolean mode) as rd "
		  + "from Subfamily s inner join SubfamilyName sn on sn.Subfamily_id=s.id "
		  + "inner join Famiglia f on f.id=s.Famiglia_id "
		  + "left join Famiglia_Designer fd on f.id=fd.Famiglia_id " 	// Left join altrimenti se la famiglia non ha un designer non si trovano risultati
		  + "left join Designer d on d.id=fd.designers_id "				// Left join altrimenti se la famiglia non ha un designer non si trovano risultati
		  + "where "
		  + (!preview ? "s.published=true and f.published=true and " : "")
		  + "sn.localeCode=:locale and (sn.value like CONCAT('%',:search,'%') or d.name LIKE CONCAT('%',:search,'%') OR d.surname like CONCAT('%',:search,'%') ) "
		  + "order by LENGTH(sn.value),sn.value "
		  + ") as q");
		yadaSql.setParameter("search", searchString).setParameter("locale", locale);
		@SuppressWarnings("unchecked")
     	List<Subfamily> found = yadaSql.nativeQuery(em, Subfamily.class)
			.setFirstResult(yadaPageRequest.getFirstResult())
			// Cerco se esistono altri dati facendo una query che prende un elemento in più
			.setMaxResults(yadaPageRequest.getMaxResults())
			.getResultList();
		if (found.isEmpty()) {
			// Riprova con la query fulltext "originale"
			return findProductsNormally(searchString, preview, yadaPageRequest);
		}
		//    	if (hasNext==true) {
		//    		@SuppressWarnings("unchecked")
		//			List<Subfamily> nextElement = yadaSql.nativeQuery(em, Subfamily.class)
		//    			.setFirstResult(nextPage.getOffset())
		//    			.setMaxResults(1)
		//    			.getResultList();
		//    		hasNext = !nextElement.isEmpty();
		//    	}
    	return new YadaPageRows<Subfamily>(found, yadaPageRequest);
    }

	/**
     * @param searchString il testo da cercare
     * @param pageable
     * @return
     */
    public YadaPageRows<Subfamily> findProductsNormally(String searchString, boolean preview, YadaPageRequest yadaPageRequest) {
    	// Questa è la query "full text search" originale che però non trova nomi troppo corti o contenenti punteggiatura, tipo "a.24".
    	String locale = LocaleContextHolder.getLocale().toString();
		YadaSql yadaSql = YadaSql.instance().selectFrom(
			"select distinct q.* from ( "
		  + "select distinct s.*, sn.value, match(sn.value) against(CONCAT(:search,'*') in boolean mode) as rn, match(d.name,d.surname) against(CONCAT(:search,'*') in boolean mode) as rd "
		  + "from Subfamily s inner join SubfamilyName sn on sn.Subfamily_id=s.id "
		  + "inner join Famiglia f on f.id=s.Famiglia_id "
		  + "left join Famiglia_Designer fd on f.id=fd.Famiglia_id " 	// Left join altrimenti se la famiglia non ha un designer non si trovano risultati
		  + "left join Designer d on d.id=fd.designers_id "				// Left join altrimenti se la famiglia non ha un designer non si trovano risultati
		  + "where "
		  + (!preview ? "s.published=true and f.published=true and " : "")
		  + "sn.localeCode=:locale and ( match(sn.value) against(CONCAT(:search,'*') in boolean mode) or match(d.name,d.surname) against(CONCAT(:search,'*') in boolean mode) ) "
//		  + "order by (rn*20+rd*10) desc "
		  + "order by LENGTH(sn.value),sn.value "
		  + ") as q");
		yadaSql.setParameter("search", searchString).setParameter("locale", locale);
		@SuppressWarnings("unchecked")
    	List<Subfamily> found = yadaSql.nativeQuery(em, Subfamily.class)
			.setFirstResult(yadaPageRequest.getFirstResult())
			.setMaxResults(yadaPageRequest.getMaxResults())
			.getResultList();
//    	Pageable nextPage = yadaPageRequest.next();
//    	boolean hasNext = found.size()==yadaPageRequest.getPageSize();
//    	if (hasNext==true) {
//    		@SuppressWarnings("unchecked")
//			List<Subfamily> nextElement = yadaSql.nativeQuery(em, Subfamily.class)
//    			.setFirstResult(nextPage.getOffset())
//    			.setMaxResults(1)
//    			.getResultList();
//    		hasNext = !nextElement.isEmpty();
//    	}
//    	Slice<Subfamily> result = new SliceImpl<>(found, nextPage, hasNext);
    	return new YadaPageRows<Subfamily>(found, yadaPageRequest);
    }

    public YadaPageRows<Project> findProjects(String searchString, boolean scenarios, YadaPageRequest yadaPageRequest) {
    		String locale = LocaleContextHolder.getLocale().toString();
		YadaSql yadaSql = YadaSql.instance().selectFrom("select distinct q.* from ( "
				+ "select distinct p.*,tpo.titlePartOne,"
				+ "match(tpo.titlePartOne) against(CONCAT(:search,'*') in boolean mode) as rt, "
				+ "match(tt.titlePartTwo) against(CONCAT(:search,'*') in boolean mode) as rtt, "
				+ "match(pl.location) against(CONCAT(:search,'*') in boolean mode) rl "
//				+ ",match(sn.value) against(CONCAT(:search,'*') in boolean mode) as rs "
				+ "from Project p inner join Project_location pl ON p.id=pl.Project_id "
				+ "inner join Project_titlePartOne tpo ON p.id=tpo.Project_id "
				+ "inner join Project_titlePartTwo tt ON p.id=tt.Project_id "
				+ "left join PageModule pm on pm.project_id=p.id and (pm.type=10 OR pm.type=11 or pm.type=5 or pm.type=8) "
				+ "left join PageModule_Subfamily pms on pms.PageModule_id=pm.id "
				+ "left join PageModule_textOne pmt on pmt.PageModule_id=pm.id "
    			+ "left join PageModule_textTwo pmtt on pmtt.PageModule_id=pm.id "
				+ "left join SubfamilyName sn on sn.Subfamily_id=pms.subfamilies_id "
				+ "where p.enabled=true and p.publishDate <= NOW() and pl.locale=:locale and tpo.locale=:locale and tt.locale=:locale "
				+ "and p.scenarios= " + scenarios + " "
				+ "and (sn.localeCode is null or sn.localeCode=:locale) "
				+ "and (pmt.textOne LIKE CONCAT('%',:search,'%') or pmtt.textTwo LIKE CONCAT('%',:search,'%') "
				+ "or tpo.titlePartOne LIKE CONCAT('%',:search,'%') or tt.titlePartTwo LIKE CONCAT('%',:search,'%') "
				+ "or pm.author LIKE CONCAT('%',:search,'%') or pm.photoby LIKE CONCAT('%',:search,'%') "
				+ "or pl.location LIKE CONCAT('%',:search,'%') or sn.value LIKE CONCAT('%',:search,'%') ) "
//				+ "order by(rt*30+rtt*30+rl*20+rs*10) desc "
//				+ "order by(rt*30+rtt*30+rl*20) desc "
				+ "order by tpo.titlePartOne "
				+ ") as q");
		yadaSql.setParameter("search", searchString).setParameter("locale", locale);
		@SuppressWarnings("unchecked")
    	List<Project> found = yadaSql.nativeQuery(em, Project.class)
			.setFirstResult(yadaPageRequest.getFirstResult())
			.setMaxResults(yadaPageRequest.getMaxResults())
			.getResultList();
//    	Pageable nextPage = yadaPageRequest.next();
//    	boolean hasNext = found.size()==yadaPageRequest.getPageSize();
//    	if (hasNext==true) {
//    		@SuppressWarnings("unchecked")
//			List<Project> nextElement = yadaSql.nativeQuery(em, Project.class)
//    			.setFirstResult(nextPage.getOffset())
//    			.setMaxResults(1)
//    			.getResultList();
//    		hasNext = !nextElement.isEmpty();
//    	}
//    	Slice<Project> result = new SliceImpl<>(found, nextPage, hasNext);
		
		YadaUtil.prefetchLocalizedStringListRecursive(found, Project.class);
	
    		return new YadaPageRows<Project>(found, yadaPageRequest);
    }

    public YadaPageRows<NewsJournal> findNewsJournal(String searchString, YadaPageRequest yadaPageRequest) {
    	String locale = LocaleContextHolder.getLocale().toString();
    	YadaSql yadaSql = YadaSql.instance().selectFrom("select distinct q.* from ( "
    			+ "select distinct n.*, npo.titlePartOne "
    			// + "match(npo.titlePartOne) against(CONCAT(:search,'*') in boolean mode) as rt, "
    			// + "match(tt.titlePartTwo) against(CONCAT(:search,'*') in boolean mode) as rtt, "
				// + "match(sn.value) against(CONCAT(:search,'*') in boolean mode) as rs "
    			+ "from NewsJournal n inner join NewsJournal_titlePartOne npo ON n.id=npo.NewsJournal_id "
    			+ "inner join NewsJournal_titlePartTwo tt ON n.id=tt.NewsJournal_id "
    			+ "left join PageModule pm on pm.project_id=n.id and (pm.type=10 OR pm.type=2 or pm.type=5 or pm.type=8) "
    			+ "left join PageModule_Subfamily pms on pms.PageModule_id=pm.id "
    			+ "left join PageModule_textOne pmt on pmt.PageModule_id=pm.id "
    			+ "left join PageModule_textTwo pmtt on pmtt.PageModule_id=pm.id "
    			+ "left join SubfamilyName sn on sn.Subfamily_id=pms.subfamilies_id "
    			+ "where n.unlisted=false and n.publishDate <= NOW() and n.enabled=true and npo.locale=:locale and tt.locale=:locale and (sn.localeCode is null or sn.localeCode=:locale) and "
    			+ "(npo.titlePartOne LIKE CONCAT('%',:search,'%') or pmt.textOne LIKE CONCAT('%',:search,'%') "
    			+ "or pmtt.textTwo LIKE CONCAT('%',:search,'%') or tt.titlePartTwo LIKE CONCAT('%',:search,'%') or pm.author LIKE CONCAT('%',:search,'%') "
    			+ "or pm.photoby LIKE CONCAT('%',:search,'%') "
    			+ "or sn.value LIKE CONCAT('%',:search,'%') ) "
//    			+ "order by(rt*30+rtt*30+rs*10) desc "
    			+ "order by npo.titlePartOne "
    			+ ") as q");
    	yadaSql.setParameter("search", searchString).setParameter("locale", locale);
    	@SuppressWarnings("unchecked")
    	List<NewsJournal> found = yadaSql.nativeQuery(em, NewsJournal.class)
    	.setFirstResult(yadaPageRequest.getFirstResult())
    	.setMaxResults(yadaPageRequest.getMaxResults())
    	.getResultList();
//    	Pageable nextPage = pageable.next();
//    	boolean hasNext = found.size()==pageable.getPageSize();
//    	if (hasNext==true) {
//    		@SuppressWarnings("unchecked")
//    		List<NewsJournal> nextElement = yadaSql.nativeQuery(em, NewsJournal.class)
//    		.setFirstResult(nextPage.getOffset())
//    		.setMaxResults(1)
//    		.getResultList();
//    		hasNext = !nextElement.isEmpty();
//    	}
//    	Slice<NewsJournal> result = new SliceImpl<>(found, nextPage, hasNext);
    	

		YadaUtil.prefetchLocalizedStringListRecursive(found, NewsJournal.class);
		
    	return new YadaPageRows<NewsJournal>(found, yadaPageRequest);
    }

    public YadaPageRows<Designer> findDesigners(String searchString, boolean preview, YadaPageRequest yadaPageRequest) {
    	String locale = LocaleContextHolder.getLocale().toString();
		YadaSql yadaSql = YadaSql.instance().selectFrom("select distinct d.* from Subfamily s")
			.join("inner join SubfamilyName sn on sn.Subfamily_id=s.id")
			.join("inner join Famiglia f on f.id=s.Famiglia_id")
			.join("inner join Famiglia_Designer fd on f.id=fd.Famiglia_id")
			.join("inner join Designer d on d.id=fd.designers_id")
			.where(!preview, "s.published=true").and()
			.where(!preview, "f.published=true").and()
			.where("sn.localeCode=:locale").and()
			.startSubexpression()
				.where("sn.value like CONCAT('%',:search,'%')").or()
				.where("d.name LIKE CONCAT('%',:search,'%')").or()
				.where("d.surname like CONCAT('%',:search,'%')").or()
			.endSubexpression()
			.orderBy("order by d.surname, d.name");
		yadaSql.setParameter("search", searchString).setParameter("locale", locale);
		@SuppressWarnings("unchecked")
    	List<Designer> found = yadaSql.nativeQuery(em,Designer.class)
			.setFirstResult(yadaPageRequest.getFirstResult())
			.setMaxResults(yadaPageRequest.getMaxResults())
			.getResultList();
		// Questo è un workaround per evitare duplicati nei designer. Chiaramente rompe la paginazione.
//		List<Designer> found = new ArrayList<>();
//		for (Designer designer : foundWithDuplicates) {
//		    if (!found.contains(designer)) {
//		    	found.add(designer);
//		    }
//		}
//    	Pageable nextPage = pageable.next();
//    	boolean hasNext = found.size()==pageable.getPageSize();
//    	if (hasNext==true) {
//    		@SuppressWarnings("unchecked")
//			List<Designer> nextElement = yadaSql.nativeQuery(em,Designer.class)
//    			.setFirstResult(nextPage.getOffset())
//    			.setMaxResults(1)
//    			.getResultList();
//    		hasNext = !nextElement.isEmpty();
//    	}
//    	Slice<Designer> result = new SliceImpl<>(found, nextPage, hasNext);
    	return new YadaPageRows<Designer>(found, yadaPageRequest);
    }

    public List<Prodotto> findBySapCode(String SapCode) {
		YadaSql yadaSql = YadaSql.instance().selectFrom("select distinct p from Prodotto p")
			.join("join p.componenti pc")
			.where("where p.stato = :stato").and()
	    	.where("pc.codiceSap = :sap").and()
	    	.where("p.subfamily.published = true").and()
	    	.where("p.countryCode is null").and() // Prende solo i prodotti "world"
	    	.where("p.subfamily.famiglia.published = true")
	    	.setParameter("sap", SapCode)
	    	.setParameter("stato", "PUBLISHED");
    	List<Prodotto> found = yadaSql.query(em,Prodotto.class).getResultList();

		YadaSql yadaSql2 = YadaSql.instance().selectFrom("select distinct p from Prodotto p")
				.join("join p.accessori pa")
				.where("where p.stato = :stato").and()
		    	.where("pa.codiceSap = :sap").and()
		    	.where("p.subfamily.published = true").and()
		    	.where("p.countryCode is null").and() // Prende solo i prodotti "world"
		    	.where("p.subfamily.famiglia.published = true")
		    	.setParameter("sap", SapCode)
		    	.setParameter("stato", "PUBLISHED");
	    	List<Prodotto> found2 = yadaSql2.query(em,Prodotto.class).getResultList();


		found.addAll(found2);

    	return found;
    }

}
