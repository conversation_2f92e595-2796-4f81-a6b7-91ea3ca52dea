package com.artemide.core;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.annotation.Order;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.web.SecurityFilterChain;

import net.yadaframework.security.YadaSecurityConfig;

/**
 * Security configuration per la sezione "Publications".
 * Da abilitare in WebApplicationInitializer
 */
@Configuration
@Order(8)
@EnableWebSecurity
// This has been disabled in WebApplicationInitializer
public class PublicationSecurityConfig extends YadaSecurityConfig {

	public PublicationSecurityConfig() {
		loginUrl = "/publications/login";
		loginUrlAjax = "/publications/login";
	}

	@Bean
	public SecurityFilterChain publicationSecurityConfigChain(HttpSecurity http) throws Exception {
		// super.failureHandler.setFailureUrlNormalRequest("/publications/login");
		// super.failureHandler.setFailureUrlAjaxRequest("/publications/login");
		// super.successHandler.setTargetUrlParameter("someParameter");
		super.successHandler.setDefaultTargetUrlNormalRequest("/download");
		super.successHandler.setDefaultTargetUrlAjaxRequest("/yadaLoginSuccess"); // No targetUrl on ajax login
		super.logoutSuccessHandler.setDefaultTargetUrl("/download"); // language path will be added in the handler

		//	The mapping matches URLs using the following rules:
		//		? matches one character
		//		* matches zero or more characters
		//		** matches zero or more 'directories' in a path
		//	Patterns which end with /** (and have no other wildcards) are optimized by using a substring match
		http
				.authorizeHttpRequests(authorize -> authorize
						// Apply specific rules for paths under "/publications"
						.requestMatchers("/publications/downloadFile").authenticated()
						.requestMatchers("/publications/downloadZip").authenticated()
						.requestMatchers("/publications/**").permitAll())
				.formLogin(form -> form
						// Configure login page and processing URL for "/publications"
						.loginPage("/publications/login")
						.loginProcessingUrl("/publications/loginPost"))
				.logout(logout -> logout
						// Configure logout URL and success handler for "/publications"
						.logoutUrl("/publications/logout")
						.logoutSuccessHandler(logoutSuccessHandler));
		super.configure(http);
		return http.build();
	}
}
