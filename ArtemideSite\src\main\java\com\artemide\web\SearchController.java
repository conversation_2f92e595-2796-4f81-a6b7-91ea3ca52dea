package com.artemide.web;

import java.util.List;

import org.apache.commons.lang3.StringUtils;
import org.hibernate.collection.spi.PersistentMap;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.RequestMapping;

import com.artemide.common.components.AmdSession;
import com.artemide.common.components.AmdUtil;
import com.artemide.common.persistence.entity.NewsJournal;
import com.artemide.common.persistence.entity.Project;
import com.artemide.common.repository.SearchDao;
import com.yr.entity.Designer;
import com.yr.entity.Prodotto;
import com.yr.entity.Subfamily;

import net.yadaframework.components.YadaWebUtil;
import net.yadaframework.web.YadaPageRequest;
import net.yadaframework.web.YadaPageRows;

@Controller
@RequestMapping("search")
public class SearchController {
	public static final int SEARCH_PAGESIZE = 4;

	private final Logger log = LoggerFactory.getLogger(getClass());

	@Autowired private SearchDao searchDao;
	@Autowired private AmdSession amdSession;
	// @Autowired private AmdConfiguration config;
	@Autowired private YadaWebUtil yadaWebUtil;
	
	/**
	 * Inizia la ricerca. Viene chiamata anche quando al "back" del browser si torna su una ricerca precedente, nel qual caso potrebbero 
	 * esserci informazioni riguardanti la paginazione fatta in precedenza.
	 * @param searchString il testo da cercare
	 * @param searchPagination informazioni di paginazione, non viene valorizzato alla prima chiamata
	 * @param model
	 * @return
	 */
	@RequestMapping("")
	public String search(String searchString, SearchPagination searchPagination, Model model) {
		boolean sSap = false; // True quando sono stati trovati risultati sap
		boolean haveResults = false; // True quando sono stati trovati risultati qualunque, sap o non sap

		if (StringUtils.isNotBlank(searchString)) {
			if (isLikeCodiceProdotto(searchString.trim())) {
				// Ricerca per codice prodotto (sap)
				haveResults = searchProductsSap(searchString.trim(), model);
				sSap = haveResults;
			}
			if (!sSap) {
				//searchString = searchString.toLowerCase().replace('à', 'a').replace('è', 'e').replace('é', 'e').replace('ì', 'i').replace('ò', 'o').replace('ù', 'u');
				searchString = searchString.replaceAll("[^\\w:.\\°\\-]", " ");
				// Remove from the search string any operator that would break the fulltext search
	    			searchString = searchString.replaceAll("[+\\-<>~()\"*]", "").trim().toLowerCase();
				if (StringUtils.isNotBlank(searchString)) {
					boolean rProducts = searchProducts(searchString, model, searchPagination.product);
					boolean rProjects = searchProjects(searchString, model, searchPagination.project);
					boolean rScenarios = searchScenarios(searchString, model, searchPagination.scenario);
					boolean rNews = searchNews(searchString, model, searchPagination.journal);
					boolean rDesigners = searchDesigners(searchString, model, searchPagination.designer);
					haveResults = (rProducts || rProjects || rNews || rDesigners || rScenarios);
				}
			}
		}

		model.addAttribute("haveResults", haveResults);
		model.addAttribute("searchString", searchString);
		model.addAttribute("sSap", sSap);

		return "/search/search";
	}

	@RequestMapping(value="/loadMoreProducts")
	public String loadMoreProducts(String searchString, Model model, YadaPageRequest yadaPageRequest) {
		searchProducts(searchString, model, yadaPageRequest);
		model.addAttribute("searchString", searchString);
		return "/search/resultNormal :: .innerProductGrid ";
	}


	@RequestMapping(value="/loadMoreProjects")
	public String loadMoreProjects(String searchString, Model model, YadaPageRequest yadaPageRequest) {
		searchProjects(searchString,model,yadaPageRequest);
		model.addAttribute("searchString",searchString);
		return "/search/resultNormal :: .innerProjectGrid ";
	}

	@RequestMapping(value="/loadMoreScenarios")
	public String loadMoreScenarios(String searchString,Model model, YadaPageRequest yadaPageRequest) {
		searchScenarios(searchString, model, yadaPageRequest);
		model.addAttribute("searchString",searchString);
		return "/search/resultNormal :: .innerScenarioGrid ";
	}

	@RequestMapping(value="/loadMoreNews")
	public String loadMoreNews(String searchString,Model model, YadaPageRequest yadaPageRequest) {
		searchNews(searchString, model, yadaPageRequest);
		model.addAttribute("searchString",searchString);
		return "/search/resultNormal :: .innerNewsGrid ";
	}

	@RequestMapping(value="/loadMoreDesigners")
	public String loadMoreDesigners(String searchString,Model model, YadaPageRequest yadaPageRequest) {
		searchDesigners(searchString, model, yadaPageRequest);
		model.addAttribute("searchString",searchString);
		return "/search/resultNormal :: .innerDesignersGrid ";
	}

	private boolean searchProducts(String searchString, Model model, YadaPageRequest yadaPageRequest) {
		try {
			YadaPageRows<Subfamily> productsFound = searchDao.findProducts(searchString, AmdUtil.isPreview(), yadaPageRequest);
			YadaPageRequest nextPageProduct = yadaPageRequest.getNextPageRequest();
			boolean hasNextProduct = productsFound.hasMoreRows();

			model.addAttribute("productsFound", productsFound);
			model.addAttribute("thisPageProducts", yadaPageRequest.getPage());
			model.addAttribute("nextPageProducts", nextPageProduct.getPage());
			model.addAttribute("pageSizeProducts", SEARCH_PAGESIZE);
			model.addAttribute("hasNextProduct", hasNextProduct);
			model.addAttribute("favouriteSubfamilies", amdSession.getFavouriteSubfamilies());
			model.addAttribute("productColumns", SEARCH_PAGESIZE);

			return !productsFound.isEmpty();
		} catch (Exception e) {
			log.error("Eccezione cercando la stringa '{}'", searchString, e);
		}
		return false;
	}

	private boolean searchProductsSap(String sapCode,Model model) {
		List<Prodotto> productsFound = searchDao.findBySapCode(sapCode);
		boolean found = false;
		// TODO non si può togliere questo loop semplicemente mettendo un "is not null" nelle query?
		for(Prodotto product : productsFound) {
			if(product!=null) {
				found = true;
				break;
			}
		}
		model.addAttribute("productsFoundSap",productsFound);
		return found;
	}

	private boolean searchProjects(String searchString,Model model, YadaPageRequest yadaPageRequest) {
		YadaPageRows<Project> projectsFound = searchDao.findProjects(searchString, false, yadaPageRequest);
		YadaPageRequest nextPageProject = yadaPageRequest.getNextPageRequest();
    	boolean hasNextProject = projectsFound.hasMoreRows();

    	model.addAttribute("projectsFound",projectsFound);
		model.addAttribute("thisPageProjects", yadaPageRequest.getPage());
		model.addAttribute("nextPageProjects", nextPageProject.getPage());
		model.addAttribute("pageSizeProjects", SEARCH_PAGESIZE);
		model.addAttribute("hasNextProjects", hasNextProject);
		model.addAttribute("favouriteProjects", amdSession.getFavouriteProjects());
		model.addAttribute("projectColumns",SEARCH_PAGESIZE);

		return !projectsFound.isEmpty();
	}

	private boolean searchScenarios(String searchString,Model model, YadaPageRequest yadaPageRequest) {
		YadaPageRows<Project> scenariosFound = searchDao.findProjects(searchString, true, yadaPageRequest);
		YadaPageRequest nextPageScenarios = yadaPageRequest.getNextPageRequest();
		boolean hasNextScenario = scenariosFound.hasMoreRows();

		model.addAttribute("scenariosFound",scenariosFound);
		model.addAttribute("thisPageScenarios", yadaPageRequest.getPage());
		model.addAttribute("nextPageScenarios", nextPageScenarios.getPage());
		model.addAttribute("pageSizeScenarios", SEARCH_PAGESIZE);
		model.addAttribute("hasNextScenarios", hasNextScenario);
		model.addAttribute("favouriteScenarios", amdSession.getFavouriteProjects());
		model.addAttribute("scenarioColumns", SEARCH_PAGESIZE);

		return !scenariosFound.isEmpty();
	}

	private boolean searchNews(String searchString,Model model, YadaPageRequest yadaPageRequest) {
		YadaPageRows<NewsJournal> newsFound = searchDao.findNewsJournal(searchString, yadaPageRequest);
		YadaPageRequest nextPageNews = yadaPageRequest.getNextPageRequest();
    	boolean hasNextNews = newsFound.hasMoreRows();

		model.addAttribute("newsFound",newsFound);
		model.addAttribute("thisPageNews", yadaPageRequest.getPage());
		model.addAttribute("nextPageNews", nextPageNews.getPage());
		model.addAttribute("pageSizeNews", SEARCH_PAGESIZE);
		model.addAttribute("hasNextNews", hasNextNews);
		model.addAttribute("newsColumns", SEARCH_PAGESIZE);

		return !newsFound.isEmpty();
	}

	private boolean searchDesigners(String searchString, Model model, YadaPageRequest yadaPageRequest) {
		YadaPageRows<Designer> designersFound = searchDao.findDesigners(searchString, AmdUtil.isPreview(), yadaPageRequest);
		YadaPageRequest nextPageDesigner = yadaPageRequest.getNextPageRequest();
		boolean hasNextDesigner = designersFound.hasMoreRows();

		model.addAttribute("designersFound", designersFound);
		model.addAttribute("thisPageDesigners", yadaPageRequest.getPage());
		model.addAttribute("nextPageDesigners", nextPageDesigner.getPage());
		model.addAttribute("pageSizeDesigners", SEARCH_PAGESIZE);
		model.addAttribute("hasNextDesigners", hasNextDesigner);
		model.addAttribute("designersColumns", SEARCH_PAGESIZE);

		return !designersFound.isEmpty();
	}

	public String getSlug(String title) {
		return yadaWebUtil.makeSlug(title);
	}

	public String getLocalizedName(PersistentMap names)
	{
		String locale = LocaleContextHolder.getLocale().toString();
		return names.get(locale).toString();
	}

	private boolean isLikeCodiceProdotto(String string){
		boolean result = false;
		if (string.toLowerCase().equals("nh1217")) {
			return result;
		}
		else {
			result = string.matches("\\w{5,18}");
			// Abbiamo tolto il match complesso perché diventava sempre più generico.
			// Adesso invece accettiamo qualunque parola entro certi limiti e cerchiamo se esistono codici che la contengono.
//			string.matches("^[a-zA-Z]{1}[0-9\\s]{1}[^\\s]*") ||
//			string.matches("^[0-9]{7}[a-zA-Z\\s]{1}[.^\\s]*")  ||
//			string.matches("^[0-9]{7}[a-zA-Z\\s]{3}[.^\\s]*")  ||
//			string.matches("^(?:NL|nl)[0-9]+[a-zA-Z0-9]*") ||
//			string.matches("^(?:DD|dd)[0-9]+[a-zA-Z0-9]*") ||
//			string.matches("^[0-9]{4}[a-zA-Z\\s]{1}[0-9]{2}[a-zA-Z\\s]{1}[.^\\s]*") ||
//			string.matches("^[a-zA-Z\\s]{2,}[0-9]{4,}[a-zA-Z\\s]{0,}[0-9]{0,}[a-zA-Z\\s]{0,}[0-9]{0,}[a-zA-Z\\s]{0,}[.^\\s]*") ||
//			string.matches("^[0-9]{4}[a-zA-Z\\s]{1}[0-9]{1,2}APP[.^\\s]*"); //1157W10APP
			return result;
		}
	}

}
