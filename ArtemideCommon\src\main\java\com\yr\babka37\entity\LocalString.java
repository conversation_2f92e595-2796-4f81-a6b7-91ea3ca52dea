package com.yr.babka37.entity;

import java.io.Serializable;

import jakarta.persistence.Column;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Lob;
import jakarta.persistence.MappedSuperclass;
import jakarta.persistence.Transient;

import org.hibernate.annotations.Type;

@MappedSuperclass
// Implementa CloneableDeep per fare in modo che i valori multilingua siano copiati e non riusati quando si duplica un oggetto
public class LocalString implements CloneableDeep, Serializable {
	private long id;
	private String localeCode;
	private String value;
	
	public LocalString() {
	}
	
	public LocalString(String locale, String value) {
		this.localeCode = locale;
		this.value = value;
	}

	@Id @GeneratedValue(strategy=GenerationType.IDENTITY)
	public Long getId() {
		return id;
	}
	
	public void setId(Long id) {
		this.id = id;
	}
	
	@Column(length=5)
	public String getLocaleCode() {
		return localeCode;
	}
	
	public void setLocaleCode(String localeCode) {
		this.localeCode = localeCode;
	}

	// Changed from @Lob to TEXT because Hibernate 7 maps @Lob to LONGTEXT which is overkill.
	// @Lob
	@Column(columnDefinition = "TEXT")
	public String getValue() {
		return value;
	}

	public void setValue(String value) {
		this.value = value;
	}

	@Override
	@Transient
	public java.lang.reflect.Field[] getExcludedFields() {
		// Nessun field escluso, ma l'interfaccia serve comunque (marker interface)
		return null;
	}

	@Override
	public String toString() {
		return value;
	}

}
