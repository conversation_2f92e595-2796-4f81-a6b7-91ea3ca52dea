# Tabelle per il Configuratore
# Son dovuto partire da V050 per non creare conflitti col branch main

create table Configurator (id bigint not null auto_increment, enabled bit not null, name varchar(255), recessedAvailable bit not null, smdAvailable bit not null, suspensionAvailable bit not null, version bigint not null, famiglia_id bigint not null, primary key (id)) engine=InnoDB;

alter table Configurator add constraint FKltply2nkit0xivh9oc5x1l1nc foreign key (famiglia_id) references Famiglia (id);

create table ConfiguratorShape (id bigint not null auto_increment, additionalWhere varchar(256), angle varchar(8), installation integer not null, length varchar(16), magnetic bit not null, pos integer not null, radius varchar(16), text1 varchar(32), text2 varchar(32), text3 varchar(32), version bigint not null, width varchar(16), cassaforma_id bigint, configurator_id bigint, icon_id bigint, model3d_id bigint, primary key (id)) engine=InnoDB;
create table YadaAttachedFile (id bigint not null auto_increment, attachedToId bigint, clientFilename varchar(255), filename varchar(255), filenameDesktop varchar(255), filenameMobile varchar(255), forLocale varchar(255), modified DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP, published bit not null, relativeFolderPath varchar(255), sortOrder bigint not null, uploadTimestamp datetime, version bigint not null, primary key (id)) engine=InnoDB;
create table YadaAttachedFile_description (YadaAttachedFile_id bigint not null, description varchar(512), locale varchar(32) not null, primary key (YadaAttachedFile_id, locale)) engine=InnoDB;
create table YadaAttachedFile_title (YadaAttachedFile_id bigint not null, title varchar(64), locale varchar(32) not null, primary key (YadaAttachedFile_id, locale)) engine=InnoDB;

alter table ConfiguratorShape add constraint FKp8j0i6587cw52gu7ejlj546t4 foreign key (cassaforma_id) references Prodotto (id);
alter table ConfiguratorShape add constraint FK4u9ogmvicb65uxti7gwk8oarx foreign key (configurator_id) references Configurator (id);
alter table ConfiguratorShape add constraint FKf9yb57xkofwk5qhnhvbi2bo6o foreign key (icon_id) references YadaAttachedFile (id);
alter table ConfiguratorShape add constraint FK2g57st9x4vga7u1v32odpgoxm foreign key (model3d_id) references YadaAttachedFile (id);

alter table YadaAttachedFile_description add constraint FKj1954nnr3hu07yak1tyb4inc6 foreign key (YadaAttachedFile_id) references YadaAttachedFile (id);
alter table YadaAttachedFile_title add constraint FKqawwx1dakd1a91pxgappdycka foreign key (YadaAttachedFile_id) references YadaAttachedFile (id);

