package com.yr.entity.localstring;

import java.io.Serializable;

import jakarta.persistence.Entity;

import com.yr.babka37.entity.LocalString;

// This is not an entity, it is just used to hold the name of the family
// @Entity
@Deprecated // To be deleted because useless
public class FamigliaNome extends LocalString implements Serializable {
	private static final long serialVersionUID = 1L;

	public FamigliaNome() {
		super();
		// TODO Auto-generated constructor stub
	}

	public FamigliaNome(String locale, String value) {
		super(locale, value);
		// TODO Auto-generated constructor stub
	}
	// Just a placeholder to name the table

	
}
