# Had to separate the timestamp in a different entity in order to prevent race conditions on the Prodotto table in the CMS 

ALTER TABLE Prodotto DROP COLUMN pdfTimestamp;

create table ProdottoPdfTimestamp (id bigint not null auto_increment, pdfTimestamp datetime(6), prodotto_id bigint not null, primary key (id)) engine=InnoDB;
alter table ProdottoPdfTimestamp add constraint UK_r89jvy4t5sdpp0y9bdktdti51 unique (prodotto_id);
alter table ProdottoPdfTimestamp add constraint FKbda14cyitfmwfwwbq11unjhsi foreign key (prodotto_id) references Prodotto (id);
