# Remove microbanner-related fields from DB

alter table HomePage2 
drop column backgroundColorMircobanner,
drop column enableMicroBanner,
drop column expirationDateMicroBanner,
drop column textColorMicroBanner;

# Remove old HomePage from DB

drop table IF EXISTS HomeGallerySubtitle, HomeGalleryTitle, HomeGalleryTitleThree, HomeGalleryTitleTwo, HomeLongBannerEventDate, HomeLongBannerEventDateTwo, 
HomeLongBannerEventLocation, HomeLongBannerEventLocationTwo, HomeLongBannerExtraLine, HomeLongBannerExtraLineTwo, HomeLongBannerLink, 
HomeLongBannerLinkTwo, HomeLongBannerSubtitle, HomeLongBannerSubtitleTwo, HomeLongBannerTitle, HomeLongBannerTitleTwo, 
HomeMicroBannerLink, HomeMicroBannerTitle, HomeNewsTitle, HomeNewsTitleMobile, HomeNewsTitleThree, HomeNewsTitleThreeMobile, 
HomeNewsTitleTwo, HomeNewsTitleTwoMobile, HomePage_SortedUploadedFiles, HomePageGallery_SortedUploadedFiles, 
HomeProjectTitle, HomeProjectTitleMobile;

drop table IF EXISTS HomePageGallery, HomePage;







