#!/bin/bash
# Questo script controlla che tutti i constraint dello schema siano stati messi dei file sql.
# Non deve più essere usato perché è sufficiente fare un diff tra amd.sql 563871f14c2be418c389c50fb34bc03f74222f7c e l'ultima
# versione, prendere gli alter table, e vedere se sono stati messi tra i file sql di questo folder.
# Si può comunque usare modificandolo per fare un check automatico.
#
# 
# Si usa creando un file "alters" che contiene tutti gli "alter table" di amd.sql
# poi si copia tale file su ubuntu insieme a questo folder e si esegue.
# Dovrebbe stampare tutti i constraint che sono dichiarati in amd.sql, e la cui tabella è presente in 
# qualche file sql, ma che non è stato usato.
# In effetti se la tabella era presente nel vecchio db e abbiamo solo aggiunto una colonna, il check viene
# saltato per cui non è preciso, ma altrimenti uscivano troppi risultati.
# L'ultimo check è stato fatto con presente "V120__Search2.sql"
#
# ATTENZIONE: Nel futuro si può semplicemente controllare che sia presente tutta la riga di "alter table" senza estrarre table e key.

filename='/tmp/alters'
while read line; do
        # echo $line;
        a=${line##alter table };
        table=${a%% add *};
        # echo $table;
        a=${line##*add constraint };
        key=${a%% *};
        # echo $key;
        tableFound=$(grep -l $table /tmp/database/*);
        if [[ "$tableFound" != "" ]]; then
                keyFound=$(grep -l $key /tmp/database/*);
                if [[ "$keyFound" == "" ]]; then
                        echo $key;
                fi
        fi
done < $filename
~
~
~
