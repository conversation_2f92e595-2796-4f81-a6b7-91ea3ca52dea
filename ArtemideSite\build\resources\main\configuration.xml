<?xml version="1.0" encoding="UTF-8"?>
<configuration>
	<header>
		<result>
			<expressionEngine config-class="org.apache.commons.configuration2.tree.xpath.XPathExpressionEngine"/>
		</result>
	</header>
	<override> <!-- Top files overwrite bottom files -->
		<!-- reloadingRefreshDelay is in milliseconds: 20000 = 20 seconds -->
		<system/>
		<env/>
		<!-- Properties local to each environment -->
		<properties fileName="/srv/amddev/bin/security.properties" config-optional="true"/>
		<properties fileName="/srv/amdcol/bin/security.properties" config-optional="true"/>
		<properties fileName="/srv/amdprod/bin/security.properties" config-optional="true"/>
		<!-- build.properties is in WEB-INF, which is the parent folder of the execution context -->
		<properties fileName="../build.properties" config-optional="true"/>
		<properties fileName="net.yadaframework.yadaweb.properties"/>
		<properties fileName="net.yadaframework.yadawebsecurity.properties" config-optional="true"/>
		<properties fileName="net.yadaframework.yadawebcms.properties" config-optional="true"/>
		<properties fileName="net.yadaframework.yadawebcommerce.properties" config-optional="true"/>
		
		<!-- Personal configuration for developers -->
		<xml config-name="localdev" config-at="config" fileName="/srv/amddev/bin/conf.webapp.localdev.xml" 
			reloadingRefreshDelay="4000" config-optional="true" config-reload="true">
		</xml>
		
		<xml config-name="dev" config-at="config" fileName="conf.webapp.dev.xml" 
			reloadingRefreshDelay="4000" config-optional="true" config-reload="true">
		</xml>
		<xml config-name="evo" config-at="config" fileName="conf.webapp.evo.xml" 
			reloadingRefreshDelay="4000" config-optional="true" config-reload="true">
		</xml>
		<xml config-name="col" config-at="config" fileName="conf.webapp.col.xml" 
			reloadingRefreshDelay="4000" config-optional="true" config-reload="true">
		</xml>
		<xml config-name="prod" config-at="config" fileName="conf.webapp.prod.xml" 
			reloadingRefreshDelay="8000" config-optional="true" config-reload="true">
		</xml>
		<xml config-name="core" config-at="config" fileName="conf.core.xml" 
			reloadingRefreshDelay="8000" config-optional="true" config-reload="true">
		</xml>
	</override>
	<additional config-name="add">
		<!-- Il config-at permette di usare "options.xxx.yyy" nel nome delle properties, 
		     mentre il config-name permette di usare cc.getConfiguration("options"); -->
		 <!-- 
		<xml config-name="globaloptions" config-at="globaloptions" fileName="conf.globaloptions.xml" config-optional="false" config-reload="false">
		</xml>
		  -->
		<xml config-name="coreoptions" config-at="coreoptions" fileName="conf.coreoptions.xml"
			reloadingRefreshDelay="8000" config-optional="true" config-reload="true">
		</xml>
		<xml config-name="coreoptions_it" config-at="coreoptions_it" fileName="conf.coreoptions.it.xml"
			reloadingRefreshDelay="8000" config-optional="true" config-reload="true">
		</xml>
		<xml config-name="coreoptions_fr" config-at="coreoptions_fr" fileName="conf.coreoptions.fr.xml"
			reloadingRefreshDelay="8000" config-optional="true" config-reload="true">
		</xml>
		<xml config-name="coreoptions_de" config-at="coreoptions_de" fileName="conf.coreoptions.de.xml"
			reloadingRefreshDelay="8000" config-optional="true" config-reload="true">
		</xml>
		<xml config-name="coreoptions_es" config-at="coreoptions_es" fileName="conf.coreoptions.es.xml"
			reloadingRefreshDelay="8000" config-optional="true" config-reload="true">
		</xml>
		<xml config-name="coreoptions_ru" config-at="coreoptions_ru" fileName="conf.coreoptions.ru.xml"
			reloadingRefreshDelay="8000" config-optional="true" config-reload="true">
		</xml>
		
		<!-- Valori messi nel db dal CMS - non sono localizzati -->
		<xml config-name="options" config-at="options" fileName="conf.options.xml" 
			reloadingRefreshDelay="8000" config-optional="true" config-reload="true">
		</xml>


	</additional>
</configuration>
