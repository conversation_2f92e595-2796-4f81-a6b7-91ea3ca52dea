<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
    <head>
        <meta charset="UTF-8"/>
        <th:block th:replace="~{/email/mailhead :: head}" ></th:block>
    </head>
    <body>
        <th:block th:replace="~{/email/mailhead :: body}" ></th:block>
        <h1 th:if="${action == 'create'}">New Publication Added</h1>
        <h1 th:if="${action == 'update'}">Publication Updated</h1>
        <h1 th:if="${action == 'delete'}">Publication Deleted</h1>
        
        <table>
            <tr><td>Title: </td><td>[[${publication.localTitle}]]</td></tr>
            <tr><td>Type: </td><td>[[${typologyName}]]</td></tr>
            <tr><td>ID: </td><td>[[${publication.id}]]</td></tr>
            <tr><td>Date: </td><td th:with="dateFormat=#{date.format.long}" th:text="${#dates.format(actionDate, dateFormat)}">2025-03-27</td></tr>
        </table>
        
        <p th:if="${action != 'delete'}">You can view this publication in the <a th:href="@{${dashboardUrl}}">administration area</a>.</p>
        
        <div th:replace="~{/email/mailfooter :: body}" ></div>
    </body>
</html>
