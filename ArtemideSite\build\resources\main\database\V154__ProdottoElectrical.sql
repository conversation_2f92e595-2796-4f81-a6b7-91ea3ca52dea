# ProdottoElectrical
create table ElectricalName (id bigint not null auto_increment, localeCode varchar(5), value longtext, Prodotto_id bigint, primary key (id)) engine=InnoDB;
alter table ElectricalName add constraint FKnjgo65gh4y6cubo2k2t4cugal foreign key (Prodotto_id) references Prodotto (id);
create table ProdottoElectrical (id bigint not null auto_increment, CCT varchar(32), CRI varchar(255), dimmable bit not null, dimmer bit not null, dimmerTypology varchar(255), efficacy varchar(32), efficiency varchar(32), emergencyDuration varchar(255), flux varchar(32), homologation varchar(255), insulationClass varchar(255), ip varchar(255), trasformerPower varchar(255), voltage varchar(255), prodotto_id bigint, primary key (id)) engine=InnoDB;
create table ProdottoElectricalBallast (id bigint not null auto_increment, localeCode varchar(5), value longtext, ProdottoElectrical_id bigint, primary key (id)) engine=InnoDB;
create table ProdottoElectricalEmergency (id bigint not null auto_increment, localeCode varchar(5), value longtext, ProdottoElectrical_id bigint, primary key (id)) engine=InnoDB;
create table ProdottoElectricalName (id bigint not null auto_increment, localeCode varchar(5), value longtext, ProdottoElectrical_id bigint, primary key (id)) engine=InnoDB;
create table ProdottoElectricalRemoteControl (id bigint not null auto_increment, localeCode varchar(5), value longtext, ProdottoElectrical_id bigint, primary key (id)) engine=InnoDB;
create table ProdottoElectricalTransformer (id bigint not null auto_increment, localeCode varchar(5), value longtext, ProdottoElectrical_id bigint, primary key (id)) engine=InnoDB;

alter table ProdottoElectrical add constraint FK3w2l3g9w8cffqxqapm003q990 foreign key (prodotto_id) references Prodotto (id);
alter table ProdottoElectricalBallast add constraint FKe872iyhh5cap42pj1lriavdco foreign key (ProdottoElectrical_id) references ProdottoElectrical (id);
alter table ProdottoElectricalEmergency add constraint FK5e8awus9klmwfviyi4ebnq2j foreign key (ProdottoElectrical_id) references ProdottoElectrical (id);
alter table ProdottoElectricalName add constraint FK6ho5i5gakk1jl1if63tjf2ee6 foreign key (ProdottoElectrical_id) references ProdottoElectrical (id);
alter table ProdottoElectricalRemoteControl add constraint FKj0ho6b4wmf6gwn4ohda0stqsa foreign key (ProdottoElectrical_id) references ProdottoElectrical (id);
alter table ProdottoElectricalTransformer add constraint FK4ebr9j3clw6fx21w7p7kxiehs foreign key (ProdottoElectrical_id) references ProdottoElectrical (id);
alter table ProdottoBallast add constraint FK1lhv6uq0gq1f7ld2767m44qtd foreign key (Prodotto_id) references Prodotto (id);
alter table ProdottoEmergency add constraint FK93on9eujmuxm6xqr33elacb4p foreign key (Prodotto_id) references Prodotto (id);
alter table ProdottoRemoteControl add constraint FKc6h65b9ummespdk13pb43ww4 foreign key (Prodotto_id) references Prodotto (id);
alter table ProdottoTransformer add constraint FKkps2i4ai2mmjmr7f0rfa4a843 foreign key (Prodotto_id) references Prodotto (id);
