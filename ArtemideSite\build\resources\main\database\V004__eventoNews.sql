create table EventoNews_multiCategory (EventoNews_id bigint not null, multiCategory integer) ENGINE=InnoDB;

alter table EventoNews_multiCategory add index FKE9C9A300D5FA2A9D (EventoNews_id), add constraint FKE9C9A300D5FA2A9D foreign key (EventoNews_id) references EventoNews (id);

create table GalleryNews (id bigint not null auto_increment, pos integer not null, eventoNew_id bigint, primary key (id)) ENGINE=InnoDB;
create table GalleryNews_SortedUploadedFiles (GalleryNews_id bigint not null, sortedUploadedFilesMap_id bigint not null, sortedUploadedFilesMap_KEY varchar(255), primary key (GalleryNews_id, sortedUploadedFilesMap_KEY), unique (sortedUploadedFilesMap_id)) ENGINE=InnoDB;

alter table GalleryNews add index FKE34B25255728CCBA (eventoNew_id), add constraint FKE34B25255728CCBA foreign key (eventoNew_id) references EventoNews (id);
alter table GalleryNews_SortedUploadedFiles add index FK86D6D60021033D80 (sortedUploadedFilesMap_id), add constraint FK86D6D60021033D80 foreign key (sortedUploadedFilesMap_id) references SortedUploadedFiles (id);
alter table GalleryNews_SortedUploadedFiles add index FK86D6D60014FC3257 (GalleryNews_id), add constraint FK86D6D60014FC3257 foreign key (GalleryNews_id) references GalleryNews (id);

alter table EventoNews drop column international;

alter table EventoNews drop column summary;

alter table EventoNews add column video varchar(256);