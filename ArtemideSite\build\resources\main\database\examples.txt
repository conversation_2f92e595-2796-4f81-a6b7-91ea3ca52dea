# Renaming a column:

alter table Certificate CHANGE oldname newname varchar(12) not null;

--> Constraints rename themselves automatically

# Adding one column to a table:

ALTER TABLE Annuncio ADD COLUMN `likes` bigint after `latitudine`;

# Adding many columns:

ALTER TABLE Annuncio
ADD COLUMN prezzoVisitaWeek integer after prezzoVisitaNight,
ADD COLUMN prezzoVisitaWeekend integer after prezzoVisitaWeek,
ADD COLUMN prezzoVisitaYear integer after prezzoVisitaWeekend;

# Change the definition of a column

ALTER TABLE Campaign MODIFY annuncio_id bigint not null;

# Removing columns

ALTER TABLE `Annuncio` 
DROP COLUMN `periodoPrezzo`,
DROP COLUMN `prezzo`;

# Changing the value of a cell

UPDATE Annuncio SET videoReady=true WHERE videoReady is null;

# Delete a table

drop table Annuncio_Tag;

# Delete a constraint (use 'index')

alter table Certificate drop index UK_jwn1h8fk2i36w92br0ctbvou0;

If the index is used by a foreign key, it can't be deleted ("errno: 150 - Foreign key constraint is incorrectly formed").
If you need to delete a unique index and get this error, then you have to drop and create the index on the same line:

alter table Publications_Subfamily drop KEY UK_p28sm2tehiuuttg7ypkf3mxdo, add key UK_p28sm2tehiuuttg7ypkf3mxdo (sottofamiglie_id);

# Delete a foreign key

alter table Certificate drop foreign key `FK_jwn1h8fk2i36w92br0ctbvou0`;

