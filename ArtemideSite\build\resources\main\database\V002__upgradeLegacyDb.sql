# Script to run for upgrading the legacy preresponsive database to the new responsive database.
# (the database must already be in utf8mb4 without hybernate_sequence)

# New Yada classes
create table YadaAutoLoginToken (id bigint not null auto_increment, expiration datetime, timestamp datetime, token bigint not null, yadaUserCredentials_id bigint, primary key (id)) engine=InnoDB;
create table YadaBrowserId (id bigint not null auto_increment, leastSigBits bigint, modified DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP, mostSigBits bigint, version bigint not null, primary key (id)) engine=InnoDB;
create table YadaClause (id bigint not null auto_increment, clauseVersion integer not null, content longtext, modified DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP, name varchar(32) not null, version bigint not null, primary key (id)) engine=InnoDB;
create table YadaJob (id bigint not null auto_increment, jobDescription varchar(256), jobGroup varchar(128), jobGroupPaused bit not null, jobLastSuccessfulRun TIMESTAMP NULL, jobName varchar(128), jobPriority integer not null, jobRecoverable bit not null, jobScheduledTime TIMESTAMP NULL, modified DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP, version bigint not null, jobStateObject_id bigint, primary key (id)) engine=InnoDB;
create table YadaJob_BeActive (YadaJob_id bigint not null, jobsMustBeActive_id bigint not null) engine=InnoDB;
create table YadaJob_BeCompleted (YadaJob_id bigint not null, jobsMustComplete_id bigint not null) engine=InnoDB;
create table YadaJob_BeInactive (YadaJob_id bigint not null, jobsMustBeInactive_id bigint not null) engine=InnoDB;
create table YadaPersistentEnum (id bigint not null auto_increment, enumClassName varchar(255) not null, enumName varchar(255) not null, enumOrdinal integer not null, primary key (id)) engine=InnoDB;
create table YadaPersistentEnum_langToText (YadaPersistentEnum_id bigint not null, localText varchar(255), language varchar(255) not null, primary key (YadaPersistentEnum_id, language)) engine=InnoDB;
create table YadaRegistrationRequest (id bigint not null auto_increment, email varchar(64) not null, modified DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP, password varchar(128) not null, registrationType integer, timestamp datetime, token bigint not null, version bigint not null, trattamentoDati_id bigint, yadaUserCredentials_id bigint, primary key (id)) engine=InnoDB;
create table YadaSocialCredentials (id bigint not null auto_increment, email varchar(128) not null, modified DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP, socialId varchar(128) not null, type integer not null, version bigint not null, yadaUserCredentials_id bigint not null, primary key (id)) engine=InnoDB;
create table YadaUserCredentials (id bigint not null auto_increment, changePassword bit not null, creationDate datetime, enabled bit not null, failedAttempts integer not null, lastFailedAttempt datetime, lastSuccessfulLogin datetime, modified DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP, password varchar(128) not null, passwordDate datetime, username varchar(128) not null, version bigint not null, primary key (id)) engine=InnoDB;
create table YadaUserCredentials_roles (YadaUserCredentials_id bigint not null, roles integer) engine=InnoDB;
create table YadaUserProfile (id bigint not null auto_increment, firstName varchar(32), lastName varchar(64), middleName varchar(32), modified DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP, timezone varchar(64), version bigint not null, userCredentials_id bigint not null, primary key (id)) engine=InnoDB;
alter table YadaBrowserId add constraint UKlvfuna79iqujxpkn0l6xvirh4 unique (mostSigBits, leastSigBits);
alter table YadaClause add constraint UKek0brxiv78vf6idvd6dv8v69d unique (name, clauseVersion);
alter table YadaPersistentEnum add constraint UKfuc71vofqasw0r57t7etipp7p unique (enumClassName, enumOrdinal);
alter table YadaSocialCredentials add constraint UK_1uppa4u7bksphbjwm4i2c8re9 unique (socialId);
alter table YadaUserCredentials add constraint UK_6gbgs7fb7g5t4wo0ys7e5q31j unique (username);
alter table YadaUserProfile add constraint UK_3bjn82k5gj41f9ocejoxx1uua unique (userCredentials_id);
alter table YadaAutoLoginToken add constraint FKh92vo7me2k2s4v1x1jercpuo foreign key (yadaUserCredentials_id) references YadaUserCredentials (id);
alter table YadaJob add constraint FKbly4fv9jmbvwppy5b9x79yokq foreign key (jobStateObject_id) references YadaPersistentEnum (id);
alter table YadaJob_BeActive add constraint FKfcdajxue4qegy3sh412qcqd7 foreign key (jobsMustBeActive_id) references YadaJob (id);
alter table YadaJob_BeActive add constraint FKqhqlee0k5m0ir9s6kpw8m9y6d foreign key (YadaJob_id) references YadaJob (id);
alter table YadaJob_BeCompleted add constraint FK8o25xd851myc035dwd0xm7kpd foreign key (jobsMustComplete_id) references YadaJob (id);
alter table YadaJob_BeCompleted add constraint FKgcmntp7yy872ldenedb6nnyep foreign key (YadaJob_id) references YadaJob (id);
alter table YadaJob_BeInactive add constraint FK8yfnn9cj06lrptwbtnpnevp4h foreign key (jobsMustBeInactive_id) references YadaJob (id);
alter table YadaJob_BeInactive add constraint FKamylqhhgf9gjwsux3yfosvq52 foreign key (YadaJob_id) references YadaJob (id);
alter table YadaPersistentEnum_langToText add constraint FKewmgshpqaehgfba9sp8pluddg foreign key (YadaPersistentEnum_id) references YadaPersistentEnum (id);
alter table YadaRegistrationRequest add constraint FKkn2yxfy3t9fjmuannqfph49d0 foreign key (trattamentoDati_id) references YadaClause (id);
alter table YadaRegistrationRequest add constraint FKq6guqxscpqqq7pl96md1y79rn foreign key (yadaUserCredentials_id) references YadaUserCredentials (id);
alter table YadaSocialCredentials add constraint FK72s54ufexgh2xk2122ihkc82l foreign key (yadaUserCredentials_id) references YadaUserCredentials (id);
alter table YadaUserCredentials_roles add constraint FK1oj60uojdn4xql004wfe2v0hp foreign key (YadaUserCredentials_id) references YadaUserCredentials (id);
alter table YadaUserProfile add constraint FKm8x7qmacvae25wmfdhnuf4e25 foreign key (userCredentials_id) references YadaUserCredentials (id);

# Cleaning up
drop table dadividere;
drop table dafixare;
drop table fixati;
drop table geoloc;


# Adding yada columns to UserCredentials - DO NOT ENABLE
# ALTER TABLE UserCredentials ADD changePassword BIT NOT NULL DEFAULT 0;
# ALTER TABLE UserCredentials ADD creationDate datetime;
# ALTER TABLE UserCredentials ADD failedAttempts integer not null DEFAULT 0;
# ALTER TABLE UserCredentials ADD lastFailedAttempt datetime;
# ALTER TABLE UserCredentials ADD lastSuccessfulLogin datetime;
# ALTER TABLE UserCredentials CHANGE password password VARCHAR(128) NOT NULL;
# ALTER TABLE UserCredentials CHANGE username username VARCHAR(128) NOT NULL;
# Renaming the old UserCredentials - DO NOT ENABLE
# ALTER TABLE UserCredentials RENAME YadaUserCredentials;
