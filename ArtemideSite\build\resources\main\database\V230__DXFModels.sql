# Add DXF models to configurator shape

alter table ConfiguratorShape 
ADD COLUMN dxf2DModel_id bigint after defaultModel_id,
ADD COLUMN dxf2DWallModel_id bigint after dxf2DModel_id,
ADD COLUMN dxf3DModel_id bigint after dxf2DWallModel_id
;

alter table ConfiguratorShape add constraint FKqkiowpan6lfdwh1r0nb2x5r4o foreign key (dxf2DModel_id) references YadaAttachedFile (id);
alter table ConfiguratorShape add constraint FKcomd9g81fnmmhw6b99g51jh foreign key (dxf2DWallModel_id) references YadaAttachedFile (id);
alter table ConfiguratorShape add constraint FKphhq668kw0nnpyniqnvkcibgl foreign key (dxf3DModel_id) references YadaAttachedFile (id);



