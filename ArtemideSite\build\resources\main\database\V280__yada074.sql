# Changes for Yada 0.7.4

-- YadaRegistrationRequest
ALTER TABLE YadaRegistrationRequest
ADD COLUMN timezone varchar(64) after `timestamp`,
CHANGE timestamp timestamp TIMESTAMP NULL;

-- YadaUserProfile
ALTER TABLE YadaUserProfile
ADD COLUMN timezoneSetByUser bit not null after `timezone`,
ADD COLUMN avatar_id bigint;
alter table YadaUserProfile add constraint FKpi28ogwa7vguwb3vv1tkmpovi foreign key (avatar_id) references YadaAttachedFile (id);

-- YadaAttachedFile
ALTER TABLE YadaAttachedFile 
ADD COLUMN metadata varchar(1024) after `width`,
CHANGE uploadTimestamp uploadTimestamp TIMESTAMP NULL;

-- YadaAutoLoginToken
ALTER TABLE YadaAutoLoginToken
CHANGE expiration expiration TIMESTAMP NULL,
CHANGE timestamp timestamp TIMESTAMP NULL;

-- YadaManagedFile
drop table YadaManagedFile;
create table YadaManagedFile (id bigint not null auto_increment, clientFilename varchar(255), description varchar(512), height integer not null, width integer not null, expirationTimestamp datetime(6), filename varchar(255), modified DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP, privateAccess bit not null, relativeFolderPath varchar(255), sizeInBytes bigint, temporary bit not null, uploadTimestamp datetime(6), version bigint not null, primary key (id)) engine=InnoDB;
create table YadaManagedFile_YadaManagedFile (YadaManagedFile_id bigint not null, derivedAssets_id bigint not null, assetKey varchar(255) not null, primary key (YadaManagedFile_id, assetKey)) engine=InnoDB;
alter table YadaManagedFile_YadaManagedFile add constraint UK_hgxut4q36gtjhq87u79qdm1hq unique (derivedAssets_id);
alter table YadaManagedFile_YadaManagedFile add constraint FK2mw9b5mxcoo5epdpw3718m9kf foreign key (derivedAssets_id) references YadaManagedFile (id);
alter table YadaManagedFile_YadaManagedFile add constraint FKexoh53ti99il5xfow6celtn0q foreign key (YadaManagedFile_id) references YadaManagedFile (id);

-- YadaUserCredentials
ALTER TABLE YadaUserCredentials
CHANGE creationDate creationDate TIMESTAMP NULL,
CHANGE lastFailedAttempt lastFailedAttempt TIMESTAMP NULL,
CHANGE lastSuccessfulLogin lastSuccessfulLogin TIMESTAMP NULL,
CHANGE passwordDate passwordDate TIMESTAMP NULL;
