# Novelties flag and name

create table CatalogConfig (id bigint not null auto_increment, enableNovelties bit, version bigint not null, primary key (id)) engine=InnoDB;
create table CatalogConfig_noveltiesName (CatalogConfig_id bigint not null, noveltiesName varchar(255), locale varchar(32) not null, primary key (CatalogConfig_id, locale)) engine=InnoDB;

alter table CatalogConfig_noveltiesName add constraint FKibw2xsh5k64cxe009m12xo6a1 foreign key (CatalogConfig_id) references CatalogConfig (id);


