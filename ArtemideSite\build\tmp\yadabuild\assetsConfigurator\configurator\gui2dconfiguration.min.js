window.configuration={app:{startUpBehaviors:["Api"],runtimeBehaviors:[],domComponents:["TopbarController","MovablesController","ContextMenuController","SnackbarComponent","SaveWarningComponent","OverviewController","PowerController","HeightController","SelectController"],containerId:"app-3d",configuratorId:null,configuratorCode:null,services:"AtLeastOnePowerSourceSlotValidator"},validation:{validatePowerSourcesInElementsStep:!0},view:{scaleRatio:1.25,theta:22,phi:30,zoomExtends:!1,displayMode:"Display.PerspectiveView",showBuildingResize:!0,showDimensions:"ShowDimensions.Vertical | ShowDimensions.Horizontal",showLabels:!1,unitType:"UnitType.Metric",showPlane:!1,backgroundColor:"#FFFFFF",backgroundColorExport:"#FFFFFF",backgroundAlpha:1,ambientLightColor:"#4D4D4C",ambientLightIntensity:1,ambientLightIntensityNight:.1,directionalLightColor:"#FFFFFF",directionalLightIntensity:.9,directionalLightIntensityNight:.1,availableActions:"AvailableActions.Insert | AvailableActions.Move | AvailableActions.Delete | AvailableActions.Rotate | AvailableActions.Properties",maxSuspension:4e3},interaction:{zoomStep:1e3,minDistPerspective:2e3,maxDistPerspective:3e4,minZoomOrtho:.1,maxZoomOrtho:4,maxUndoSteps:40},powerSettings:{sharpingOrDiffused:[4439942,4439164]},scene:{plane:{minSize:12e4,thin:1,thinColor:"#d7d7d7",thick:3,thickColor:"#bebebe"}},dimensions:{color:"#333333",fontSize:60,lineOffset:160,minDistanceToShow:300,minDistanceToAddUnits:700,minDistanceToAddArrows:300,textMargin:0,dimTypes:[]},building:{defaultWallFinish:"#FFFFFF",wallsTopFinish:"#161719",defaultItemColor:"#ffffff",defaultItemEdgesColor:"#000000",suspensionCableColor:"gray"},sizes:{baseHeight:10,wallThickness:100,partitionThicknessMm:120,totalDefaultHeight:2500,rangeOptions:{Default:{minWidth:2e3,maxWidth:1e4,minDepth:2e3,maxDepth:8e3,minHeight:null,maxHeight:null,resizeStep:100,resizeStepBuildingX:100,resizeStepBuildingY:100,resizeStepHeight:100,maxWidthAdmin:16e3,maxDepthAdmin:16e3,minHeightAdmin:2500,maxHeightAdmin:3500,resizeStepAdmin:100,resizeStepBuildingXAdmin:100,resizeStepBuildingYAdmin:100,resizeStepHeightAdmin:100}},resizeFloorplanAlgorithm:"ResizeFpSingleWall",resizeFloorplanArrowSize:1e3,resizeFloorplanArrowDistance:800,resizeFloorplanArrowMargin:1300,resizeFpGraphics:"ResizeFpInGraphics"},buildingTemplates:[{range:"Room",template:"001",floor:[{x:0,y:0,z:3e3},{x:6e3,y:0,z:3e3},{x:6e3,y:4e3,z:3e3},{x:0,y:4e3,z:3e3},{x:0,y:0,z:3e3}],intFinishes:["#FFFFFF","#FFFFFF","#FFFFFF","#FFFFFF"],extFinishes:["#D0D0D0","#D0D0D0","#D0D0D0","#D0D0D0"],baseFinish:"#696969",height:3e3,baseHeight:100,roofWidth:100,wallThickness:100,movables:[]}],visualStates:[],movables:[{type:"Group",skin:{graphics3D:"EmptyGraphics",data:{finishes:["highlight"]}}}],textures:{invisible:{transparent:!0,opacity:0},initial:{color:"#0000FF"},highlight:{color:"#f44141",opacity:.3},dropzone:{color:"#caf441",opacity:.2,transparent:!0},invalid:{color:"#f45c42"},detached:{color:"yellow"},selected:{color:"#008080"},powerActive:{color:"green"},powerConnected:{color:"#ace35b"}},messages:{insertError1:"Compatible Target Missing",insertError2:"This item can not be inserted into the current drawing",nothingToUndo:"There are no steps to undo",nothingToRedo:"There are no steps to redo",cmRotate:"Rotate",brushSelectSource:"Select source item",brushSelectTarget:"Select target",brushCopied:"Copied!",elementRemoved:"Element(s) removed",rulerStart:"Click on starting point",undoToRevert:"Click undo to revert",cannotResize:"Cannot resize due to the lack of space"},subfamilyMap:{4438260:{text:"Magnetic",type:"Magnetic",color:"#fb5b5b"},4439164:{text:"Diffused",type:"Diffused",color:"#42E2FF"},4439942:{text:"Sharping",type:"Sharping",color:"yellow"}}};const addedMovables=[{type:"A.24_Neutro_Linear_1176mm_T20",width:27,depth:1176,height:54,placementMode:"PlacementMode.Ceiling",offset:100,snaps:[{x:0,y:0,z:-588,side:{x:0,y:0,z:-1}},{x:0,y:0,z:588,side:{x:0,y:0,z:1}}],holderPoints:[{x:0,y:-27,z:-588},{x:0,y:-27,z:588}],childrenTypes:["Vector_Magnetic_30","Vector_Magnetic_40","Vector_Magnetic_55","Vector_PendantMagnetic_30","Vector_PendantMagnetic_40","Vector_PendantMagnetic_55","Vector_ZoomMagnetic_55"],behavior:{rotateAlgorithm:"Rotate15Degrees",placementOptions:["PlacementMode.Ceiling","PlacementMode.Walls"],alternatives:["A.24_Neutro_Linear_2352mm_T20"]},skin:{graphics3D:"CadFileGraphics",data:{finishes:["../models3d/neutro/A24-Neutro-Linear-1176mm.gltf"]}}},{type:"A.24_Neutro_Linear_2352mm_T20",width:27,depth:2352,height:54,placementMode:"PlacementMode.Ceiling",offset:100,snaps:[{x:0,y:0,z:-1176,side:{x:0,y:0,z:-1}},{x:0,y:0,z:1176,side:{x:0,y:0,z:1}}],holderPoints:[{x:0,y:-27,z:-1176},{x:0,y:-27,z:1176}],childrenTypes:["Vector_Magnetic_30","Vector_Magnetic_40","Vector_Magnetic_55","Vector_PendantMagnetic_30","Vector_PendantMagnetic_40","Vector_PendantMagnetic_55","Vector_ZoomMagnetic_55"],behavior:{rotateAlgorithm:"Rotate15Degrees",placementOptions:["PlacementMode.Ceiling","PlacementMode.Walls"],alternatives:["A.24_Neutro_Linear_1176mm_T20"]},skin:{graphics3D:"CadFileGraphics",data:{finishes:["../models3d/neutro/A24-Neutro-Linear-2352mm.gltf"]}}},{type:"A.24_Neutro_90angle_Sameplane_T20",width:315,depth:315,height:54,placementMode:"PlacementMode.Ceiling",offset:100,snaps:[{x:-144,y:0,z:157.5,side:{x:0,y:0,z:1}},{x:157.5,y:0,z:-144,side:{x:1,y:0,z:0}}],holderPoints:[{x:-144,y:-27,z:-157.5},{x:-144,y:-27,z:157.5},{x:-144,y:-27,z:-144},{x:157.5,y:-27,z:-144}],childrenTypes:["Vector_Magnetic_30","Vector_Magnetic_40","Vector_Magnetic_55","Vector_PendantMagnetic_30","Vector_PendantMagnetic_40","Vector_PendantMagnetic_55","Vector_ZoomMagnetic_55"],parts:["Part_01","Part_02"],behavior:{rotateAlgorithm:"Rotate15Degrees",placementOptions:["PlacementMode.Ceiling","PlacementMode.Walls"],is90SamePlaneAngle:!0},skin:{graphics3D:"CadFileGraphics",data:{finishes:["../models3d/neutro/A24-Neutro-90angle-Sameplane.gltf"]}}},{type:"A.24_Neutro_90angle_Perpplanes_T20",width:27,depth:330,height:330,placementMode:"PlacementMode.ExternalWalls",offset:100,snaps:[{x:0,y:138,z:165,side:{x:0,y:0,z:1}},{x:0,y:-165,z:-138,side:{x:0,y:-1,z:0}}],holderPoints:[{x:0,y:111,z:165},{x:0,y:111,z:-120}],childrenTypes:["Vector_Magnetic_30","Vector_Magnetic_40","Vector_Magnetic_55","Vector_PendantMagnetic_30","Vector_PendantMagnetic_40","Vector_PendantMagnetic_55","Vector_ZoomMagnetic_55"],parts:["Part_01","Part_02"],behavior:{placementOptions:["PlacementMode.ExternalWalls","PlacementMode.Corners"],is90PerpPlaneAngle:!0},skin:{graphics3D:"CadFileGraphics",data:{finishes:["../models3d/neutro/A24-Neutro-90angle-Perpplanes.gltf"]}}},{type:"A.24_Neutro_Curved_R561_60_T20",width:300,depth:498,height:54,placementMode:"PlacementMode.Ceiling",offset:100,snaps:[{x:-136,y:0,z:249,side:{x:0,y:0,z:1}},{x:143.3,y:0,z:-237.5,side:{x:.8660253028382761,y:0,z:-.5000001748438416}}],behavior:{rotateAlgorithm:"Rotate15Degrees",placementOptions:["PlacementMode.Ceiling","PlacementMode.Walls"]},skin:{graphics3D:"CadFileGraphics",data:{finishes:["../models3d/neutro/A24-Neutro-Curved-R561-60.gltf"]}}},{type:"A.24_Neutro_Curved_R561_90_T20",width:574,depth:574,height:54,placementMode:"PlacementMode.Ceiling",offset:100,snaps:[{x:-274,y:0,z:287,side:{x:0,y:0,z:1}},{x:287,y:0,z:-274,side:{x:1,y:0,z:0}}],behavior:{rotateAlgorithm:"Rotate15Degrees",placementOptions:["PlacementMode.Ceiling","PlacementMode.Walls"]},skin:{graphics3D:"CadFileGraphics",data:{finishes:["../models3d/neutro/A24-Neutro-Curved-R561-90.gltf"]}}},{type:"A.24_Neutro_Curved_R750_45_T20",width:242,depth:540,height:54,placementMode:"PlacementMode.Ceiling",offset:100,snaps:[{x:-108,y:0,z:270,side:{x:0,y:0,z:1}},{x:112,y:0,z:-260,side:{x:.7071067811865475,y:0,z:-.7071067811865475}}],behavior:{rotateAlgorithm:"Rotate15Degrees",placementOptions:["PlacementMode.Ceiling","PlacementMode.Walls"]},skin:{graphics3D:"CadFileGraphics",data:{finishes:["../models3d/neutro/A24-Neutro-Curved-R750-45.gltf"]}}},{type:"A.24_Neutro_Curved_R750_90_T20",width:764,depth:764,height:54,placementMode:"PlacementMode.Ceiling",offset:100,snaps:[{x:-368,y:0,z:382,side:{x:0,y:0,z:1}},{x:382,y:0,z:-368,side:{x:1,y:0,z:0}}],behavior:{rotateAlgorithm:"Rotate15Degrees",placementOptions:["PlacementMode.Ceiling","PlacementMode.Walls"]},skin:{graphics3D:"CadFileGraphics",data:{finishes:["../models3d/neutro/A24-Neutro-Curved-R750-90.gltf"]}}},{type:"A.24_Neutro_Linear_1176mm_T5",width:27,depth:1176,height:54,placementMode:"PlacementMode.Ceiling",offset:60,ceilingDistance:-40,snaps:[{x:0,y:0,z:-588,side:{x:0,y:0,z:-1}},{x:0,y:0,z:588,side:{x:0,y:0,z:1}}],holderPoints:[{x:0,y:-27,z:-588},{x:0,y:-27,z:588}],childrenTypes:["Vector_Magnetic_30","Vector_Magnetic_40","Vector_Magnetic_55","Vector_PendantMagnetic_30","Vector_PendantMagnetic_40","Vector_PendantMagnetic_55","Vector_ZoomMagnetic_55"],behavior:{rotateAlgorithm:"Rotate15Degrees",placementOptions:["PlacementMode.Ceiling","PlacementMode.Walls"],alternatives:["A.24_Neutro_Linear_2352mm_T5"]},skin:{graphics3D:"CadFileGraphics",data:{finishes:["../models3d/neutro/A24-Neutro-Linear-1176mm.gltf"]}}},{type:"A.24_Neutro_Linear_2352mm_T5",width:27,depth:2352,height:54,placementMode:"PlacementMode.Ceiling",offset:60,ceilingDistance:-40,snaps:[{x:0,y:0,z:-1176,side:{x:0,y:0,z:-1}},{x:0,y:0,z:1176,side:{x:0,y:0,z:1}}],holderPoints:[{x:0,y:-27,z:-1176},{x:0,y:-27,z:1176}],childrenTypes:["Vector_Magnetic_30","Vector_Magnetic_40","Vector_Magnetic_55","Vector_PendantMagnetic_30","Vector_PendantMagnetic_40","Vector_PendantMagnetic_55","Vector_ZoomMagnetic_55"],behavior:{rotateAlgorithm:"Rotate15Degrees",placementOptions:["PlacementMode.Ceiling","PlacementMode.Walls"],alternatives:["A.24_Neutro_Linear_1176mm_T5"]},skin:{graphics3D:"CadFileGraphics",data:{finishes:["../models3d/neutro/A24-Neutro-Linear-2352mm.gltf"]}}},{type:"A.24_Neutro_90angle_Sameplane_T5",width:315,depth:315,height:54,placementMode:"PlacementMode.Ceiling",offset:60,ceilingDistance:-40,snaps:[{x:-144,y:0,z:157.5,side:{x:0,y:0,z:1}},{x:157.5,y:0,z:-144,side:{x:1,y:0,z:0}}],holderPoints:[{x:-144,y:-27,z:-157.5},{x:-144,y:-27,z:157.5},{x:-144,y:-27,z:-144},{x:157.5,y:-27,z:-144}],childrenTypes:["Vector_Magnetic_30","Vector_Magnetic_40","Vector_Magnetic_55","Vector_PendantMagnetic_30","Vector_PendantMagnetic_40","Vector_PendantMagnetic_55","Vector_ZoomMagnetic_55"],parts:["Part_01","Part_02"],behavior:{rotateAlgorithm:"Rotate15Degrees",placementOptions:["PlacementMode.Ceiling","PlacementMode.Walls"],is90SamePlaneAngle:!0},skin:{graphics3D:"CadFileGraphics",data:{finishes:["../models3d/neutro/A24-Neutro-90angle-Sameplane.gltf"]}}},{type:"A.24_Neutro_90angle_Perpplanes_T5",width:27,depth:330,height:330,placementMode:"PlacementMode.ExternalWalls",offset:60,ceilingDistance:-40,snaps:[{x:0,y:138,z:165,side:{x:0,y:0,z:1}},{x:0,y:-165,z:-138,side:{x:0,y:-1,z:0}}],holderPoints:[{x:0,y:111,z:165},{x:0,y:111,z:-120}],childrenTypes:["Vector_Magnetic_30","Vector_Magnetic_40","Vector_Magnetic_55","Vector_PendantMagnetic_30","Vector_PendantMagnetic_40","Vector_PendantMagnetic_55","Vector_ZoomMagnetic_55"],parts:["Part_01","Part_02"],behavior:{placementOptions:["PlacementMode.ExternalWalls","PlacementMode.Corners"],is90PerpPlaneAngle:!0},skin:{graphics3D:"CadFileGraphics",data:{finishes:["../models3d/neutro/A24-Neutro-90angle-Perpplanes.gltf"]}}},{type:"A.24_Neutro_Curved_R561_60_T5",width:300,depth:498,height:54,placementMode:"PlacementMode.Ceiling",offset:60,ceilingDistance:-40,snaps:[{x:-136,y:0,z:249,side:{x:0,y:0,z:1}},{x:143.3,y:0,z:-237.5,side:{x:.8660253028382761,y:0,z:-.5000001748438416}}],behavior:{rotateAlgorithm:"Rotate15Degrees",placementOptions:["PlacementMode.Ceiling","PlacementMode.Walls"]},skin:{graphics3D:"CadFileGraphics",data:{finishes:["../models3d/neutro/A24-Neutro-Curved-R561-60.gltf"]}}},{type:"A.24_Neutro_Curved_R561_90_T5",width:574,depth:574,height:54,placementMode:"PlacementMode.Ceiling",offset:60,ceilingDistance:-40,snaps:[{x:-274,y:0,z:287,side:{x:0,y:0,z:1}},{x:287,y:0,z:-274,side:{x:1,y:0,z:0}}],behavior:{rotateAlgorithm:"Rotate15Degrees",placementOptions:["PlacementMode.Ceiling","PlacementMode.Walls"]},skin:{graphics3D:"CadFileGraphics",data:{finishes:["../models3d/neutro/A24-Neutro-Curved-R561-90.gltf"]}}},{type:"A.24_Neutro_Curved_R750_45_T5",width:242,depth:540,height:54,placementMode:"PlacementMode.Ceiling",offset:60,ceilingDistance:-40,snaps:[{x:-108,y:0,z:270,side:{x:0,y:0,z:1}},{x:112,y:0,z:-260,side:{x:.7071067811865475,y:0,z:-.7071067811865475}}],behavior:{rotateAlgorithm:"Rotate15Degrees",placementOptions:["PlacementMode.Ceiling","PlacementMode.Walls"]},skin:{graphics3D:"CadFileGraphics",data:{finishes:["../models3d/neutro/A24-Neutro-Curved-R750-45.gltf"]}}},{type:"A.24_Neutro_Curved_R750_90_T5",width:764,depth:764,height:54,placementMode:"PlacementMode.Ceiling",offset:60,ceilingDistance:-40,snaps:[{x:-368,y:0,z:382,side:{x:0,y:0,z:1}},{x:382,y:0,z:-368,side:{x:1,y:0,z:0}}],behavior:{rotateAlgorithm:"Rotate15Degrees",placementOptions:["PlacementMode.Ceiling","PlacementMode.Walls"]},skin:{graphics3D:"CadFileGraphics",data:{finishes:["../models3d/neutro/A24-Neutro-Curved-R750-90.gltf"]}}},{type:"A.24_Neutro_Linear_1176mm_T1",width:27,depth:1176,height:54,placementMode:"PlacementMode.Ceiling",offset:100,floorDistance:2300,snaps:[{x:0,y:0,z:-588,side:{x:0,y:0,z:-1}},{x:0,y:0,z:588,side:{x:0,y:0,z:1}}],holderPoints:[{x:0,y:-27,z:-588},{x:0,y:-27,z:588}],childrenTypes:["Vector_Magnetic_30","Vector_Magnetic_40","Vector_Magnetic_55","Vector_PendantMagnetic_30","Vector_PendantMagnetic_40","Vector_PendantMagnetic_55","Vector_ZoomMagnetic_55"],behavior:{rotateAlgorithm:"Rotate15Degrees",placementOptions:["PlacementMode.Ceiling"],alternatives:["A.24_Neutro_Linear_2352mm_T1"]},skin:{graphics3D:"CadFileGraphics",data:{finishes:["../models3d/neutro/A24-Neutro-Linear-1176mm.gltf"]}}},{type:"A.24_Neutro_Linear_2352mm_T1",width:27,depth:2352,height:54,placementMode:"PlacementMode.Ceiling",offset:100,floorDistance:2300,snaps:[{x:0,y:0,z:-1176,side:{x:0,y:0,z:-1}},{x:0,y:0,z:1176,side:{x:0,y:0,z:1}}],holderPoints:[{x:0,y:-27,z:-1176},{x:0,y:-27,z:1176}],childrenTypes:["Vector_Magnetic_30","Vector_Magnetic_40","Vector_Magnetic_55","Vector_PendantMagnetic_30","Vector_PendantMagnetic_40","Vector_PendantMagnetic_55","Vector_ZoomMagnetic_55"],behavior:{rotateAlgorithm:"Rotate15Degrees",placementOptions:["PlacementMode.Ceiling"],alternatives:["A.24_Neutro_Linear_1176mm_T1"]},skin:{graphics3D:"CadFileGraphics",data:{finishes:["../models3d/neutro/A24-Neutro-Linear-2352mm.gltf"]}}},{type:"A.24_Neutro_90angle_Sameplane_T1",width:315,depth:315,height:54,placementMode:"PlacementMode.Ceiling",offset:100,floorDistance:2300,snaps:[{x:-144,y:0,z:157.5,side:{x:0,y:0,z:1}},{x:157.5,y:0,z:-144,side:{x:1,y:0,z:0}}],holderPoints:[{x:-144,y:-27,z:-157.5},{x:-144,y:-27,z:157.5},{x:-144,y:-27,z:-144},{x:157.5,y:-27,z:-144}],childrenTypes:["Vector_Magnetic_30","Vector_Magnetic_40","Vector_Magnetic_55","Vector_PendantMagnetic_30","Vector_PendantMagnetic_40","Vector_PendantMagnetic_55","Vector_ZoomMagnetic_55"],parts:["Part_01","Part_02"],behavior:{rotateAlgorithm:"Rotate15Degrees",placementOptions:["PlacementMode.Ceiling"],is90SamePlaneAngle:!0},skin:{graphics3D:"CadFileGraphics",data:{finishes:["../models3d/neutro/A24-Neutro-90angle-Sameplane.gltf"]}}},{type:"A.24_Neutro_Curved_R561_60_T1",width:300,depth:498,height:54,placementMode:"PlacementMode.Ceiling",offset:100,floorDistance:2300,snaps:[{x:-136,y:0,z:249,side:{x:0,y:0,z:1}},{x:143.3,y:0,z:-237.5,side:{x:.8660253028382761,y:0,z:-.5000001748438416}}],behavior:{rotateAlgorithm:"Rotate15Degrees",placementOptions:["PlacementMode.Ceiling"]},skin:{graphics3D:"CadFileGraphics",data:{finishes:["../models3d/neutro/A24-Neutro-Curved-R561-60.gltf"]}}},{type:"A.24_Neutro_Curved_R561_90_T1",width:574,depth:574,height:54,placementMode:"PlacementMode.Ceiling",offset:100,floorDistance:2300,snaps:[{x:-274,y:0,z:287,side:{x:0,y:0,z:1}},{x:287,y:0,z:-274,side:{x:1,y:0,z:0}}],behavior:{rotateAlgorithm:"Rotate15Degrees",placementOptions:["PlacementMode.Ceiling"]},skin:{graphics3D:"CadFileGraphics",data:{finishes:["../models3d/neutro/A24-Neutro-Curved-R561-90.gltf"]}}},{type:"A.24_Neutro_Curved_R750_45_T1",width:242,depth:540,height:54,placementMode:"PlacementMode.Ceiling",offset:100,floorDistance:2300,snaps:[{x:-108,y:0,z:270,side:{x:0,y:0,z:1}},{x:112,y:0,z:-260,side:{x:.7071067811865475,y:0,z:-.7071067811865475}}],behavior:{rotateAlgorithm:"Rotate15Degrees",placementOptions:["PlacementMode.Ceiling"]},skin:{graphics3D:"CadFileGraphics",data:{finishes:["../models3d/neutro/A24-Neutro-Curved-R750-45.gltf"]}}},{type:"A.24_Neutro_Curved_R750_90_T1",width:764,depth:764,height:54,placementMode:"PlacementMode.Ceiling",offset:100,floorDistance:2300,snaps:[{x:-368,y:0,z:382,side:{x:0,y:0,z:1}},{x:382,y:0,z:-368,side:{x:1,y:0,z:0}}],behavior:{rotateAlgorithm:"Rotate15Degrees",placementOptions:["PlacementMode.Ceiling"]},skin:{graphics3D:"CadFileGraphics",data:{finishes:["../models3d/neutro/A24-Neutro-Curved-R750-90.gltf"]}}},{type:"Vector_Magnetic_30",width:71,depth:108,height:93,placementMode:"PlacementMode.Attachment",attachmentPoints:[{x:0,y:31,z:54,side:{x:0,y:0,z:1}},{x:0,y:31,z:-54,side:{x:1,y:0,z:0}}],parentPlacement:["PlacementMode.Ceiling","PlacementMode.Walls","PlacementMode.ExternalWalls"],skin:{graphics3D:"CadFileGraphics",data:{finishes:["../models3d/magnetic-attachments/Vector-Magnetic-30.gltf"]}}},{type:"Vector_Magnetic_40",width:94,depth:108,height:124,placementMode:"PlacementMode.Attachment",attachmentPoints:[{x:0,y:47,z:54,side:{x:0,y:0,z:1}},{x:0,y:47,z:-54,side:{x:1,y:0,z:0}}],parentPlacement:["PlacementMode.Ceiling","PlacementMode.Walls","PlacementMode.ExternalWalls"],skin:{graphics3D:"CadFileGraphics",data:{finishes:["../models3d/magnetic-attachments/Vector-Magnetic-40.gltf"]}}},{type:"Vector_Magnetic_55",width:119,depth:119,height:154,placementMode:"PlacementMode.Attachment",attachmentPoints:[{x:0,y:63,z:60,side:{x:0,y:0,z:1}},{x:0,y:63,z:-60,side:{x:1,y:0,z:0}}],parentPlacement:["PlacementMode.Ceiling","PlacementMode.Walls","PlacementMode.ExternalWalls"],skin:{graphics3D:"CadFileGraphics",data:{finishes:["../models3d/magnetic-attachments/Vector-Magnetic-55.gltf"]}}},{type:"Vector_PendantMagnetic_30",width:30,depth:108,height:652,placementMode:"PlacementMode.Attachment",attachmentPoints:[{x:0,y:311,z:54,side:{x:0,y:0,z:1}},{x:0,y:311,z:-54,side:{x:1,y:0,z:0}}],parentPlacement:["PlacementMode.Ceiling","PlacementMode.ExternalWalls"],skin:{graphics3D:"CadFileGraphics",data:{finishes:["../models3d/magnetic-attachments/Vector-PendantMagnetic-30.gltf"]}}},{type:"Vector_PendantMagnetic_40",width:40,depth:108,height:678,placementMode:"PlacementMode.Attachment",attachmentPoints:[{x:0,y:324,z:54,side:{x:0,y:0,z:1}},{x:0,y:324,z:-54,side:{x:1,y:0,z:0}}],parentPlacement:["PlacementMode.Ceiling","PlacementMode.ExternalWalls"],skin:{graphics3D:"CadFileGraphics",data:{finishes:["../models3d/magnetic-attachments/Vector-PendantMagnetic-40.gltf"]}}},{type:"Vector_PendantMagnetic_55",width:55,depth:108,height:704,placementMode:"PlacementMode.Attachment",attachmentPoints:[{x:0,y:337,z:54,side:{x:0,y:0,z:1}},{x:0,y:337,z:-54,side:{x:1,y:0,z:0}}],parentPlacement:["PlacementMode.Ceiling","PlacementMode.ExternalWalls"],skin:{graphics3D:"CadFileGraphics",data:{finishes:["../models3d/magnetic-attachments/Vector-PendantMagnetic-55.gltf"]}}},{type:"Vector_ZoomMagnetic_55",width:117,depth:117,height:152,placementMode:"PlacementMode.Attachment",attachmentPoints:[{x:0,y:63,z:59,side:{x:0,y:0,z:1}},{x:0,y:63,z:-59,side:{x:1,y:0,z:0}}],parentPlacement:["PlacementMode.Ceiling","PlacementMode.Walls","PlacementMode.ExternalWalls"],skin:{type:"Vector_ZoomMagnetic_55",graphics3D:"CadFileGraphics",data:{finishes:["../models3d/magnetic-attachments/Vector-ZoomMagnetic-55.gltf"]}}},{type:"AoL_Suspension_Curve_45_T1",width:262.34571838378906,depth:548.0052490234375,height:50.01527976989746,placementMode:"PlacementMode.Horizontal",offset:100,ceilingDistance:400,snaps:[{x:-113.762,y:0,z:-256.575,side:{x:-.7071067811865475,y:0,z:-.7071067811865475}},{x:106.221,y:0,z:274.1,side:{x:0,y:0,z:1}}],behavior:{smdAlternative:"AoL_Ceiling_Curve_45_T20",rotateAlgorithm:"Rotate15Degrees",placementOptions:["PlacementMode.Horizontal","PlacementMode.Ceiling"],singlePowerSource:!0,noSnap:["AoL_Ceiling_Curve_90_Down_T1","AoL_Ceiling_Curve_90_Up_T1","AoL_Ceiling_Curve_90_Down_T20","AoL_Ceiling_Curve_90_Up_T20"],suspensionCables:2,applyMaxSuspension:!0},skin:{graphics3D:"CadFileGraphics",data:{finishes:["../models3d/aol/AoL_Suspension_Curve_45.gltf"]}}},{type:"AoL_Suspension_Curve_60_T1",width:250.00165557861328,depth:389.70579528808594,height:50.016761779785156,placementMode:"PlacementMode.Horizontal",offset:100,ceilingDistance:400,snaps:[{x:-112.2,y:0,z:-173.2,side:{x:-.8660253028382761,y:0,z:-.5000001748438416}},{x:100.078,y:0,z:194.853,side:{x:0,y:0,z:1}}],behavior:{smdAlternative:"AoL_Ceiling_Curve_60_T20",rotateAlgorithm:"Rotate15Degrees",placementOptions:["PlacementMode.Horizontal","PlacementMode.Ceiling"],singlePowerSource:!0,noSnap:["AoL_Ceiling_Curve_90_Down_T1","AoL_Ceiling_Curve_90_Up_T1","AoL_Ceiling_Curve_90_Down_T20","AoL_Ceiling_Curve_90_Up_T20"],suspensionCables:2,applyMaxSuspension:!0},skin:{graphics3D:"CadFileGraphics",data:{finishes:["../models3d/aol/AoL_Suspension_Curve_60.gltf"]}}},{type:"Giunto_90_T1",width:68,depth:68,height:50,placementMode:"PlacementMode.Horizontal",offset:100,ceilingDistance:400,snaps:[{x:34,y:0,z:9,side:{x:1,y:0,z:0}},{x:-9,y:0,z:-34,side:{x:0,y:0,z:-1}}],behavior:{rotateAlgorithm:"Rotate15Degrees",placementOptions:["PlacementMode.Horizontal","PlacementMode.Ceiling"],typologies:["AoL_T1"],canHavePowerSource:!1,canBeTerminal:!1,insertBetweenMaxDistance:150,snapsTo:["AoL_Suspension_Linear_600_T1","AoL_Suspension_Linear_1200_T1","AoL_Suspension_Linear_1800_T1"],applyMaxSuspension:!0,suspensionCables:1,noSuspensionCablesOnConnections:!0},skin:{graphics3D:"CadFileGraphics",data:{finishes:["../models3d/neutro/A24-Neutro-Curved-R750-90.gltf"]}}},{type:"AoL_Ceiling_Curve_90_Down_T1",width:50.00980758666992,depth:319.14012145996094,height:319.15106201171875,placementMode:"PlacementMode.Horizontal",offset:100,ceilingDistance:400,ceilingDropZoneOffset:300,snaps:[{x:0,y:134.568,z:159.569,side:{x:0,y:0,z:1}},{x:0,y:-159.576,z:-134.849,side:{x:0,y:-1,z:0}}],behavior:{rotateAlgorithm:"Rotate15Degrees",typologies:["AoL_T1","AoL_T20"],placementOptions:["PlacementMode.Horizontal","PlacementMode.Ceiling"],singlePlacementOptions:["PlacementMode.ExternalWalls"],canHavePowerSource:!1,noSnap:["AoL_Suspension_Curve_45_T1","AoL_Suspension_Curve_60_T1","AoL_Ceiling_Curve_45_T20","AoL_Ceiling_Curve_60_T20"],applyMaxSuspension:!0},skin:{graphics3D:"CadFileGraphics",data:{finishes:["../models3d/aol/AoL_Ceiling_Curve_90_Down.gltf"]}}},{type:"AoL_Ceiling_Curve_90_Up_T1",width:50.009803771972656,depth:319.138,height:319.133,placementMode:"PlacementMode.Horizontal",offset:100,ceilingDistance:400,ceilingDropZoneOffset:300,snaps:[{x:0,y:-134.569,z:159.569,side:{x:0,y:0,z:1}},{x:0,y:159.566,z:-134.561,side:{x:0,y:1,z:0}}],behavior:{rotateAlgorithm:"Rotate15Degrees",typologies:["AoL_T1","AoL_T20"],placementOptions:["PlacementMode.Horizontal","PlacementMode.Ceiling"],singlePlacementOptions:["PlacementMode.ExternalWalls"],canHavePowerSource:!1,noSnap:["AoL_Suspension_Curve_45_T1","AoL_Suspension_Curve_60_T1","AoL_Ceiling_Curve_45_T20","AoL_Ceiling_Curve_60_T20","AoL_Ceiling_Linear_600_T20","AoL_Ceiling_Linear_1200_T20","AoL_Ceiling_Linear_1800_T20"],applyMaxSuspension:!0},skin:{graphics3D:"CadFileGraphics",data:{finishes:["../models3d/aol/AoL_Ceiling_Curve_90_Up.gltf"]}}},{type:"AoL_Suspension_Linear_600_T1",width:50,depth:592,height:50,placementMode:"PlacementMode.Horizontal",offset:100,ceilingDistance:400,snaps:[{x:0,y:0,z:-296,side:{x:0,y:0,z:-1}},{x:0,y:0,z:296,side:{x:0,y:0,z:1}}],templateVersion:1,versions:[{version:0,template:{width:50,depth:588,height:50,snaps:[{x:0,y:0,z:-294,side:{x:0,y:0,z:-1}},{x:0,y:0,z:294,side:{x:0,y:0,z:1}}]}}],behavior:{smdAlternative:"AoL_Ceiling_Linear_600_T20",rotateAlgorithm:"Rotate15Degrees",placementOptions:["PlacementMode.Horizontal","PlacementMode.Ceiling"],singlePowerSourceWhenTerminal:!0,alternatives:["AoL_Suspension_Linear_1200_T1","AoL_Suspension_Linear_1800_T1"],canHavePowerSource:!1,canHavePowerSourceLast:!0,suspensionCables:2,applyMaxSuspension:!0},skin:{graphics3D:"CadFileGraphics",data:{finishes:["../models3d/aol/AoL_Suspension_Linear_600.gltf"]}}},{type:"AoL_Suspension_Linear_1200_T1",width:50,depth:1184,height:50,placementMode:"PlacementMode.Horizontal",offset:100,ceilingDistance:400,snaps:[{x:0,y:0,z:-592,side:{x:0,y:0,z:-1}},{x:0,y:0,z:592,side:{x:0,y:0,z:1}}],templateVersion:1,versions:[{version:0,template:{width:50,depth:1180,height:50,snaps:[{x:0,y:0,z:-590,side:{x:0,y:0,z:-1}},{x:0,y:0,z:590,side:{x:0,y:0,z:1}}]}}],behavior:{smdAlternative:"AoL_Ceiling_Linear_1200_T20",rotateAlgorithm:"Rotate15Degrees",placementOptions:["PlacementMode.Horizontal","PlacementMode.Ceiling"],singlePowerSourceWhenTerminal:!0,alternatives:["AoL_Suspension_Linear_600_T1","AoL_Suspension_Linear_1800_T1"],suspensionCables:2,applyMaxSuspension:!0},skin:{graphics3D:"CadFileGraphics",data:{finishes:["../models3d/aol/AoL_Suspension_Linear_1200.gltf"]}}},{type:"AoL_Suspension_Linear_1800_T1",width:50,depth:1776,height:50,placementMode:"PlacementMode.Horizontal",offset:100,ceilingDistance:400,snaps:[{x:0,y:0,z:-888,side:{x:0,y:0,z:-1}},{x:0,y:0,z:888,side:{x:0,y:0,z:1}}],templateVersion:1,versions:[{version:0,template:{width:50,depth:1772.0413818359375,height:50,snaps:[{x:0,y:0,z:-886.0206909179688,side:{x:0,y:0,z:-1}},{x:0,y:0,z:886.0206909179688,side:{x:0,y:0,z:1}}]}}],behavior:{smdAlternative:"AoL_Ceiling_Linear_1800_T20",rotateAlgorithm:"Rotate15Degrees",placementOptions:["PlacementMode.Horizontal","PlacementMode.Ceiling"],singlePowerSourceWhenTerminal:!0,alternatives:["AoL_Suspension_Linear_600_T1","AoL_Suspension_Linear_1200_T1"],suspensionCables:3,applyMaxSuspension:!0},skin:{graphics3D:"CadFileGraphics",data:{finishes:["../models3d/aol/AoL_Suspension_Linear_1800.gltf"]}}},{type:"AoL_Ceiling_Curve_45_T20",width:262.34571838378906,depth:548.0052490234375,height:50.01527976989746,placementMode:"PlacementMode.Ceiling",offset:100,snaps:[{x:-113.762,y:0,z:-256.575,side:{x:-.7071067811865475,y:0,z:-.7071067811865475}},{x:106.221,y:0,z:274.1,side:{x:0,y:0,z:1}}],behavior:{suspensionAlternative:"AoL_Suspension_Curve_45_T1",rotateAlgorithm:"Rotate15Degrees",placementOptions:["PlacementMode.Ceiling","PlacementMode.Walls"],singlePowerSource:!0,noSnap:["AoL_Ceiling_Curve_90_Down_T1","AoL_Ceiling_Curve_90_Up_T1","AoL_Ceiling_Curve_90_Down_T20","AoL_Ceiling_Curve_90_Up_T20"],applyMaxSuspension:!0},skin:{graphics3D:"CadFileGraphics",data:{finishes:["../models3d/aol/AoL_Suspension_Curve_45.gltf"]}}},{type:"AoL_Ceiling_Curve_60_T20",width:250.00165557861328,depth:389.70579528808594,height:50.016761779785156,placementMode:"PlacementMode.Ceiling",offset:100,snaps:[{x:-112.2,y:0,z:-173.2,side:{x:-.8660253028382761,y:0,z:-.5000001748438416}},{x:100.078,y:0,z:194.853,side:{x:0,y:0,z:1}}],behavior:{suspensionAlternative:"AoL_Suspension_Curve_60_T1",rotateAlgorithm:"Rotate15Degrees",placementOptions:["PlacementMode.Ceiling","PlacementMode.Walls"],singlePowerSource:!0,noSnap:["AoL_Ceiling_Curve_90_Down_T1","AoL_Ceiling_Curve_90_Up_T1","AoL_Ceiling_Curve_90_Down_T20","AoL_Ceiling_Curve_90_Up_T20"],applyMaxSuspension:!0},skin:{graphics3D:"CadFileGraphics",data:{finishes:["../models3d/aol/AoL_Suspension_Curve_60.gltf"]}}},{type:"Giunto_90_T20",width:68,depth:68,height:50,placementMode:"PlacementMode.Ceiling",offset:100,snaps:[{x:34,y:0,z:9,side:{x:1,y:0,z:0}},{x:-9,y:0,z:-34,side:{x:0,y:0,z:-1}}],behavior:{rotateAlgorithm:"Rotate15Degrees",placementOptions:["PlacementMode.Ceiling","PlacementMode.Walls"],typologies:["AoL_T20"],canHavePowerSource:!1,canBeTerminal:!1,insertBetweenMaxDistance:150,snapsTo:["AoL_Ceiling_Linear_600_T20","AoL_Ceiling_Linear_1200_T20","AoL_Ceiling_Linear_1800_T20"]},skin:{graphics3D:"CadFileGraphics",data:{finishes:["../models3d/neutro/A24-Neutro-Curved-R750-90.gltf"]}}},{type:"AoL_Ceiling_Curve_90_Down_T20",width:50.00980758666992,depth:319.14012145996094,height:319.15106201171875,placementMode:"PlacementMode.Ceiling",offset:100,ceilingDropZoneOffset:300,snaps:[{x:0,y:134.568,z:159.569,side:{x:0,y:0,z:1}},{x:0,y:-159.576,z:-134.849,side:{x:0,y:-1,z:0}}],behavior:{rotateAlgorithm:"Rotate15Degrees",typologies:["AoL_T1","AoL_T20"],placementOptions:["PlacementMode.Horizontal","PlacementMode.Ceiling"],singlePlacementOptions:["PlacementMode.ExternalWalls"],canHavePowerSource:!1,noSnap:["AoL_Suspension_Curve_45_T1","AoL_Suspension_Curve_60_T1","AoL_Ceiling_Curve_45_T20","AoL_Ceiling_Curve_60_T20"],applyMaxSuspension:!0},skin:{graphics3D:"CadFileGraphics",data:{finishes:["../models3d/aol/AoL_Ceiling_Curve_90_Down.gltf"]}}},{type:"AoL_Ceiling_Curve_90_Up_T20",width:50.009803771972656,depth:319.138,height:319.133,placementMode:"PlacementMode.Ceiling",offset:100,ceilingDropZoneOffset:300,snaps:[{x:0,y:-134.569,z:159.569,side:{x:0,y:0,z:1}},{x:0,y:159.566,z:-134.561,side:{x:0,y:1,z:0}}],behavior:{rotateAlgorithm:"Rotate15Degrees",typologies:["AoL_T1","AoL_T20"],placementOptions:["PlacementMode.Horizontal","PlacementMode.Ceiling"],singlePlacementOptions:["PlacementMode.ExternalWalls"],canHavePowerSource:!1,noSnap:["AoL_Suspension_Curve_45_T1","AoL_Suspension_Curve_60_T1","AoL_Ceiling_Curve_45_T20","AoL_Ceiling_Curve_60_T20","AoL_Ceiling_Linear_600_T20","AoL_Ceiling_Linear_1200_T20","AoL_Ceiling_Linear_1800_T20"],applyMaxSuspension:!0},skin:{graphics3D:"CadFileGraphics",data:{finishes:["../models3d/aol/AoL_Ceiling_Curve_90_Up.gltf"]}}},{type:"AoL_Ceiling_Linear_600_T20",width:50,depth:592,height:50,placementMode:"PlacementMode.Ceiling",offset:100,snaps:[{x:0,y:0,z:-296,side:{x:0,y:0,z:-1}},{x:0,y:0,z:296,side:{x:0,y:0,z:1}}],templateVersion:1,versions:[{version:0,template:{width:50,depth:588,height:50,snaps:[{x:0,y:0,z:-294,side:{x:0,y:0,z:-1}},{x:0,y:0,z:294,side:{x:0,y:0,z:1}}]}}],behavior:{suspensionAlternative:"AoL_Suspension_Linear_600_T1",rotateAlgorithm:"Rotate15Degrees",placementOptions:["PlacementMode.Ceiling","PlacementMode.Walls"],singlePowerSourceWhenTerminal:!0,alternatives:["AoL_Ceiling_Linear_1200_T20","AoL_Ceiling_Linear_1800_T20"],canHavePowerSource:!1,canHavePowerSourceLast:!0,applyMaxSuspension:!0},skin:{graphics3D:"CadFileGraphics",data:{finishes:["../models3d/aol/AoL_Suspension_Linear_600.gltf"]}}},{type:"AoL_Ceiling_Linear_1200_T20",width:50,depth:1184,height:50,placementMode:"PlacementMode.Ceiling",offset:100,snaps:[{x:0,y:0,z:-592,side:{x:0,y:0,z:-1}},{x:0,y:0,z:592,side:{x:0,y:0,z:1}}],templateVersion:1,versions:[{version:0,template:{width:50,depth:1180,height:50,snaps:[{x:0,y:0,z:-590,side:{x:0,y:0,z:-1}},{x:0,y:0,z:590,side:{x:0,y:0,z:1}}]}}],behavior:{suspensionAlternative:"AoL_Suspension_Linear_1200_T1",rotateAlgorithm:"Rotate15Degrees",placementOptions:["PlacementMode.Ceiling","PlacementMode.Walls"],singlePowerSourceWhenTerminal:!0,alternatives:["AoL_Ceiling_Linear_600_T20","AoL_Ceiling_Linear_1800_T20"],applyMaxSuspension:!0},skin:{graphics3D:"CadFileGraphics",data:{finishes:["../models3d/aol/AoL_Suspension_Linear_1200.gltf"]}}},{type:"AoL_Ceiling_Linear_1800_T20",width:50,depth:1776,height:50,placementMode:"PlacementMode.Ceiling",offset:100,snaps:[{x:0,y:0,z:-888,side:{x:0,y:0,z:-1}},{x:0,y:0,z:888,side:{x:0,y:0,z:1}}],templateVersion:1,versions:[{version:0,template:{width:50,depth:1772.0413818359375,height:50,snaps:[{x:0,y:0,z:-886.0206909179688,side:{x:0,y:0,z:-1}},{x:0,y:0,z:886.0206909179688,side:{x:0,y:0,z:1}}]}}],behavior:{suspensionAlternative:"AoL_Suspension_Linear_1800_T1",rotateAlgorithm:"Rotate15Degrees",placementOptions:["PlacementMode.Ceiling","PlacementMode.Walls"],singlePowerSourceWhenTerminal:!0,alternatives:["AoL_Ceiling_Linear_600_T20","AoL_Ceiling_Linear_1200_T20"],applyMaxSuspension:!0},skin:{graphics3D:"CadFileGraphics",data:{finishes:["../models3d/aol/AoL_Suspension_Linear_1800.gltf"]}}}];window.movableTemplate={},addedMovables.forEach(x=>{window.movableTemplate[x.type]=x}),window.configuration.mockup=!0,function(gui2d){"use strict";gui2d.configuratorId=null,gui2d.configuratorCode=null,gui2d.hideModalLoader=!1;var globalOptions={};function registerWizardHandlers(){$("#wizard .collapse").on("hide.bs.collapse",function(){$(this).parent().find(".toggler").removeClass("current"),$("#contextmenu").hide()}),$("#wizard .collapse").on("show.bs.collapse",function(){if(!wizardStepChangeRequested($(this).closest(".step")))return!1;$("#contextmenu").hide(),$(this).parent().find(".toggler").addClass("current")}),$("#wizard .typology input").click(function(e){var type,value=$(this).val();if(!isEmpty())return yada.confirm(null,"Changing installation type will erase the drawing. Proceed?",function(proceed){var installation,href,roomSize,factor;proceed&&(installation=value,href=location.href,roomSize=engine.getRoomSize(),factor="Metric"==roomSize.unitType?10:25.4,href=(href=href.replace(/(.+?\/configurator\/).+/,"$1"))+"workspace?configuratorId="+gui2d.configuratorId+"&height="+Math.round(roomSize.height.value/factor)+"&width="+Math.round(roomSize.width.value/factor)+"&depth="+Math.round(roomSize.depth.value/factor)+"&units="+roomSize.unitType+"&installation="+installation,location.href=href)},"Delete","Back"),!1;type=value,$("#wizard .elements:not(.accessories) .icons").removeClass("shown"),$("#wizard .elements:not(.accessories) .icons.type"+type).addClass("shown")}),$(".nextStep").click(function(){var $nextStep=$(this).closest(".step").next();$(".collapse",$nextStep).collapse("show")})}function wizardChangeStep(stepId){$(stepId+" h2 button").click()}function wizardStepChangeRequested($nextStep,nextStepId){$("#tooltip").hide(),$(".wizardChoice").hide();var allowed=!1;switch(nextStepId=null==$nextStep?nextStepId:$nextStep.attr("id")){case"stepInstallation":engine.setAvailableActions(null),allowed=!0;break;case"stepElements":(allowed=function(){var allowed=$("#wizard .typology input:checked").length>0||2==gui2d.configuratorId;if(!allowed){var errorMessage="Please choose an installation type before continuing";yada.showErrorModal("Choose an installation",errorMessage)}return allowed}())&&(gui2d.isAol()?engine.setAvailableActions(["Insert","Move","Delete","Rotate"]):gui2d.isA24()?engine.setAvailableActions(["Insert","Move","Delete","Rotate","Properties"]):gui2d.isHoy()?engine.setAvailableActions(["Insert","Move","Delete","Rotate",{action:"Properties",filter:movableItems=>movableItems.every(movableItem=>movableItem.data.chooseEmission),callback:gui2d.openProperties}]):engine.setAvailableActions(["Insert","Move","Delete","Rotate",{action:"Properties",callback:gui2d.openProperties}]));break;case"stepAttachments":(allowed=checkElementPlaced()&&validateAolSingleModuleConfiguration()&&checkSubfamilyEmissionChosen())&&engine.setAvailableActions(["Insert","Move","Delete","Rotate"]);break;case"stepAccessories":allowed=checkElementPlaced()&&validateAolSingleModuleConfiguration(),(allowed&=!0)&&engine.setAvailableActions(null);break;case"stepOptical":(allowed=checkElementPlaced()&&validateAolSingleModuleConfiguration()&&checkSubfamilyEmissionChosen()&&function(){var allowed=engine.validateTerminalAoLUpDowns();if(!allowed){var errorMessage="The configuration cannot be ended with the up/down terminal module.";yada.showErrorModal("Configuration not allowed",errorMessage)}return allowed}()&&function(){var allowed=engine.validateTerminalAngles();if(!allowed){var errorMessage="The configuration cannot be ended with the angular module.";yada.showErrorModal("Configuration not allowed",errorMessage)}return allowed}()&&function(){if(!window.configuration.validation||!window.configuration.validation.validatePowerSourcesInElementsStep)return!0;return function(validations){const errorMessages=engine.validate(validations).filter(x=>!x.valid).map(x=>x.message);if(errorMessages.length){const errorMessage=errorMessages.join("<br />");yada.showErrorModal("Configuration not allowed",errorMessage)}return 0===errorMessages.length}([{type:"AtLeastOnePowerSourceSlot",message:"The configuration doesn't have any power supply slots."}])}())&&(gui2d.isA24()||gui2d.isAol()?engine.setAvailableActions(["Properties"]):engine.setAvailableActions([{action:"Properties",callback:gui2d.openProperties}]));break;case"stepFinishes":(allowed=checkElementPlaced()&&validateAolSingleModuleConfiguration()&&checkSubfamilyEmissionChosen()&&checkOpticalChosen())&&(gui2d.isA24()||gui2d.isAol()?engine.setAvailableActions(["Properties"]):engine.setAvailableActions([{action:"Properties",callback:gui2d.openProperties}]));break;case"stepControl":if(allowed=checkElementPlaced()&&validateAolSingleModuleConfiguration(),allowed&=checkFinishChosen(),allowed&=checkOpticalChosen()){if(gui2d.isHoy()){engine.setAvailableActions([{action:"GroupSelect",filter:movs=>movs.some(movableItem=>3!=movableItem.data.emission),callback:gui2d.openProperties},"Power"]);!function(title,message,severity,redirectUrl){yada.loaderOff(),$("#yada-notification .modal-title").replaceWith("<div class='modal-title pleaseNoteIcon'></div>"),$("#yada-notification .modal-body").html('<p class="pleaseNoteMessage">'+message+"</p>"),$("#yada-notification:hidden").modal("show"),null!=redirectUrl&&$("#yada-notification").on("hidden.bs.modal",function(e){window.location.replace(redirectUrl)});$("#yada-notification").on("hidden.bs.modal",function(){$("#yada-notification .modal-title").replaceWith('<p class="modal-title"></p>')})}(0,"The configurator does not perform maximum power calculations on the feed-through wiring and/or the number of DALI addresses.<br>In case of particularly large installations it is necessary to have a specific electrical plan to verify the number and position of the supply points.")}else engine.setAvailableActions(["Power"]);!function(){if(engine.removeInvalidPowerSources()){var errorMessage="One or more power supplies have been removed due to changes in the configuration.";yada.showErrorModal("Power supplies removed",errorMessage)}}()}break;case"stepPrint":if(allowed=(allowed=(allowed=checkElementPlaced()&&validateAolSingleModuleConfiguration()&&checkOpticalChosen())&&checkFinishChosen())&&function(){var valid=!0;gui2d.isA24()||gui2d.isAol()?(valid=engine.powerStepValidate())||yada.showErrorModal("Property Missing",textPropertyMissingStepLightingControl):(valid=checkPropertiesSet([{flag:data=>null!=data.emissione&&3!=data.emissione,property:"lightingControl"}],textLightingControlMissing))&&gui2d.isHoy()&&((valid=engine.validateHoyPowerSources())||yada.showErrorModal("Property Missing",textPowerSourceMissingStepLightingControl));return valid}()){engine.setAvailableActions(null);var missingProductIds=engine.getState().design.movableItems.filter(movableItem=>0==movableItem.data.productIds.length);missingProductIds.length>0&&(yada.loaderOn(),gui2d.findProperties(missingProductIds))}}return allowed&&"stepControl"===gui2d.currentStep()&&engine.clearAll(),allowed}function checkElementPlaced(){var placed=!isEmpty();if(!placed){yada.showErrorModal("Room Empty","Please drag some elements to the room before continuing")}return!!window.configuration.mockup||placed}function isEmpty(){return 0==engine.getState().design.movableItems.length}function validateAolSingleModuleConfiguration(){if(1===engine.getState().design.movableItems.length&&gui2d.isAol()){const errorMessage="It is not possible to create configurations composed of only 1 module. Stand-alone versions are available on <a href='http://artemide.com/' target='_blank'>Artemide.com</a>.";return yada.showErrorModal("Configuration not allowed",errorMessage),!1}return!0}globalOptions.powerChoice={},gui2d.productConfigurationId=null,gui2d.productConfigurationName="",gui2d.urlExportConversion="",gui2d.clickedItemArray=null,gui2d.northAmerica=!1,gui2d.isA24=function(){return gui2d.configuratorCode==gui2d.codeA24},gui2d.isAol=function(){return gui2d.configuratorCode==gui2d.codeAol},gui2d.isHoy=function(){return gui2d.configuratorCode==gui2d.codeHoy},gui2d.setInitialAvailableActions=function(){wizardStepChangeRequested(null,["stepInstallation","stepElements","stepInstallation","stepInstallation"][gui2d.configuratorId-1])};var textPropertyMissing="Click on each module of your configuration and select the icon <i class='conficon-setting'></i> in order to set the element properties before proceeding to the next step. <br/><br/>Selecting the icon <i class='conficon-deselect-all'></i>  from the toolbar and then the icon <i class='conficon-setting'></i>  you will be able to set the common properties of all elements in your configuration.",textPropertyMissingStepLightingControl="Select one of the available dots close to the modules in your configuration to insert the  supply, choose between the options and then click on the modules to power them. <br/><br/>To insert a new power supply, click on a new dot and repeat the process.",textPowerSourceMissingStepLightingControl="Select any of the available dots close to the modules in your configuration to insert a power supply: at least one should be set for each connected configuration.",textLightingControlMissing="Select the configuration by clicking on a single module and choose between the lighting control options.";function checkSubfamilyEmissionChosen(){return checkPropertiesSet(["subfamilyId",{flag:"chooseEmission",property:"emissione"}],textPropertyMissing,!0)}function checkOpticalChosen(){return checkPropertiesSet([{flag:"chooseOptical",property:"optical.chosen"}],textPropertyMissing)}function checkFinishChosen(){return!!gui2d.stepFinishesHidden||checkPropertiesSet(["finish",{flag:"needsSharpingColor",property:"sharpingColor"}],textPropertyMissing)}function checkPropertiesSet(propertyNames,errorMessage,skipAttachments){for(var valid=!0,movables=engine.getState().design.movableItems,i=0;i<movables.length;i++){var movable=movables[i];if(!skipAttachments||movable.placementMode!=engine.PlacementMode.Attachment)for(var clone=Object.assign({},movable.data),k=0;k<propertyNames.length;k++){var propertyName=propertyNames[k],checkName=null;"object"==typeof propertyName&&(checkName=propertyName.flag,propertyName=propertyName.property);var propertyValue=getProperty(clone,propertyName),isFunction="function"==typeof checkName,checkFlag=null==checkName||clone[checkName]||!1;if(isFunction&&(checkFlag=checkName(clone)),1==checkFlag&&null==propertyValue){clone.invalid=!0,valid=!1,engine.clearSelection(),engine.updateItemProperties(movable.id,clone);break}}}return valid||yada.showErrorModal("Property Missing",errorMessage),!!window.configuration.mockup||valid}function openFinalPage(){yada.loaderOn(),window.setTimeout(gui2d.sendFinalData,100)}function getSceneDataName(){return"sceneData"+gui2d.configuratorId}function setDeepVal(obj,path,val){for(var props=path.split("."),i=0,n=props.length-1;i<n;++i)obj=obj[props[i]]=obj[props[i]]||{};return obj[props[i]]=val,obj}function getProperty(obj,path){for(var segments=path.split("."),i=0;i<segments.length&&null!=obj;i++){obj=obj[segments[i]]}return obj}function openPartsList(responseText){if(responseText.errorMessage)return void yada.showErrorModal(responseText.errorTitle,responseText.errorMessage);engine.setLabels(responseText.labels),engine.showLabels(!0),engine.updateScene({view:{phi:90,theta:0,displayMode:"TopView"}});var screenshot=engine.screenshot(screen.width,screen.height);sessionStorage.setItem("TopView",screenshot),engine.updateScene({view:{phi:45,theta:45,displayMode:"PerspectiveView"}}),screenshot=engine.screenshot(screen.width,screen.height),sessionStorage.setItem("PerspectiveView",screenshot),engine.updateScene({view:{phi:0,theta:0,displayMode:"FrontView"}}),screenshot=engine.screenshot(screen.width,screen.height),sessionStorage.setItem("FrontView",screenshot),engine.updateScene({view:{phi:0,theta:90,displayMode:"SideView"}}),screenshot=engine.screenshot(screen.width,screen.height),sessionStorage.setItem("SideView",screenshot),engine.showLabels(!1),engine.updateScene({view:{phi:45,theta:45,displayMode:"PerspectiveView"}});const formData=new FormData;formData.append("topView",sessionStorage.getItem("TopView")),formData.append("perspectiveView",sessionStorage.getItem("PerspectiveView")),formData.append("frontView",sessionStorage.getItem("FrontView")),formData.append("sideView",sessionStorage.getItem("SideView")),yada.ajax(gui2d.urlFinalPage,formData,null,"POST")}function dirtyFlagTrick(filename){return engine.isUnsaved()?(engine.setUnsaved(!1),function(responseText,responseHtml){window.setTimeout(function(){engine.setUnsaved(!0)},2e3)}):null}function showServerDownloadLink(format,filename){makeDownloadLink("javascript:gui2d.convert('"+format+"')",filename);$(".exportModal").addClass("result")}function makeDownloadLink(href,filename){var $linkText=$(".exportModal .downloadLinkText").html(),$link=$("<a class='downloadLink'></a>");return $link.html($linkText),$link.attr("href",href),$link.attr("download",filename),$(".exportModal a.downloadLink").remove(),$(".exportModal .downloadLinkText").parent().append($link),$link}function isCacheSupported(){return"caches"in window}$("#goFinalPage").click(function(){if(engine.isUnsaved()){var data={openFinalPage:!0};yada.ajax(gui2d.urlSaveModal,data)}else openFinalPage()}),gui2d.resetPropertyAll=function(propertyName){engine.getState().design.movableItems.forEach(movableItem=>gui2d.setMovableItemProperty(movableItem,propertyName,null))},gui2d.sendFinalData=function(){var formData=new FormData,jsonConnections=JSON.stringify(engine.getRelativeConnections());formData.append("jsonConnections",jsonConnections),null!=gui2d.productConfigurationId&&formData.append("productConfigurationId",gui2d.productConfigurationId),formData.append("configuratorId",gui2d.configuratorId),yada.ajax(gui2d.urlAssemble,formData,openPartsList,"POST")},gui2d.setRoomSize=function(widthmm,depthmm,heightmm,unitType){0!=widthmm&&0!=depthmm&&0!=heightmm&&engine.setRoomSize({width:widthmm,depth:depthmm,height:heightmm}),engine.updateScene({view:{unitType:unitType}}),globalOptions.unitType=unitType},gui2d.currentStep=function(){return $("div.collapse.show").closest(".step").attr("id")},gui2d.resetProperties=function(choiceId,movableId){switch(choiceId){case"subfamilyChanged":gui2d.setMovableProperty(movableId,"emissione",null),gui2d.setMovableProperty(movableId,"magnetic",null),gui2d.setMovableProperty(movableId,"chooseEmission",null);case"emissionChanged":var chooseOpticalInitialValue=gui2d.findMovable(movableId).data.chooseOpticalInitialValue||null;gui2d.setMovableProperty(movableId,"chooseOptical",chooseOpticalInitialValue);case"opticalChanged":gui2d.setMovableProperty(movableId,"optical",{});case"finishChanged":gui2d.setMovableProperty(movableId,"finish",null),gui2d.setMovableProperty(movableId,"productIds",[]),gui2d.setMovableProperty(movableId,"errorMessage",null);break;default:console.error("Invalid choiceId in resetProperties: "+choiceId)}engine.setUnsaved(!0)},gui2d.openProperties=function(clickedItemArray){gui2d.clickedItemArray=clickedItemArray,$(".wizardChoice").hide();var url=gui2d.urlOpenProperties+"/"+gui2d.currentStep(),toSend=gui2d.prepareJsonRequest(clickedItemArray);yada.ajax(url,toSend,null,"POST",null,gui2d.hideModalLoader)},gui2d.findProperties=function(movableArray,handler){if(null==movableArray&&(movableArray=gui2d.clickedItemArray),movableArray&&movableArray.length&&null!=movableArray[0].data.subfamilyId){var toSend=gui2d.prepareJsonRequest(movableArray);toSend.installation=gui2d.getCurrentInstallation(),yada.ajax(gui2d.urlFindProperties,toSend,function(responseJson,responseHtml){movableArray=engine.getState().design.movableItems.filter(movableItem=>movableArray.some(x=>x.id===movableItem.id));for(var i=0;i<movableArray.length;i++){var movable=movableArray[i],newProperties=responseJson[movable.id],clone=jQuery.extend(!0,{},movable.data,newProperties);clone.productIds=newProperties.productIds,engine.updateItemProperties(movable.id,clone)}null!=handler&&handler(responseJson),yada.loaderOff()},"POST")}},gui2d.prepareJsonRequest=function(clickedItemArray){null==clickedItemArray&&(clickedItemArray=gui2d.clickedItemArray);var elementData=clickedItemArray.map(function(movable){var smallMovable={};return smallMovable.id=movable.id,smallMovable.parentId=movable.parentId,smallMovable.data=movable.data,smallMovable.databaseId=movable.databaseId,null==movable.databaseId&&console.error("Missing databaseId for element "+movable.id),smallMovable.type=movable.type,smallMovable.data.attachment=movable.placementMode==engine.PlacementMode.Attachment,smallMovable});return{configuratorId:gui2d.configuratorId,configuratorCode:gui2d.configuratorCode,jsonArray:JSON.stringify(elementData)}},gui2d.loadServerData=function(saveName,sceneJson,globalOptionsJson,id){gui2d.productConfigurationName=saveName,gui2d.productConfigurationId=id,globalOptions=JSON.parse(globalOptionsJson),engine.load(JSON.parse(sceneJson)),null!=globalOptions.unitType&&engine.updateScene({view:{unitType:globalOptions.unitType}}),gui2d.findProperties(engine.getState().design.movableItems);const isTemporaryConfiguration=null==id;gui2d.setInitialAvailableActions(),isTemporaryConfiguration||wizardChangeStep("#stepElements")},gui2d.saveToServer=function(saveName,serverUrl,openFinalPageParam){var scene=engine.save(),data={saveName:saveName,scene:JSON.stringify(scene),globalOptions:JSON.stringify(globalOptions),installation:gui2d.getCurrentInstallation(),configuratorId:gui2d.configuratorId,productConfigurationId:gui2d.productConfigurationId,openingFinalPage:openFinalPageParam};yada.ajax(serverUrl,data,function(saveName,openFinalPageParam){return function(responseText,responseHtml){var result=yada.getEmbeddedResult(responseHtml);gui2d.productConfigurationId=result.productConfigurationId,gui2d.productConfigurationName=saveName,engine.setUnsaved(!1),openFinalPageParam&&openFinalPage()}}(saveName,openFinalPageParam),"POST")},gui2d.convert=function(format){if("gltf"!=format&&"glb"!=format&&"obj"!=format&&"ply"!=format&&"stl"!=format&&"dae"!=format){if("csv"==format)return(data={}).productConfigurationId=gui2d.productConfigurationId,data.configuratorId=gui2d.configuratorId,void yada.ajax(gui2d.urlCsvDownload,data,null,"POST",null,!1,!1,"blob");if("xls"==format)return(data={}).productConfigurationId=gui2d.productConfigurationId,data.configuratorId=gui2d.configuratorId,void yada.ajax(gui2d.urlXlsDownload,data,null,"POST",null,!1,!1,"blob");if("dxf"==format){const newFilename=gui2d.productConfigurationName+"."+format;var data=new FormData;const sourceJson=new Blob([JSON.stringify(engine.getDxfExportData())],{type:"application/json"});return data.append("sourceJson",sourceJson),data.append("productConfigurationId",gui2d.productConfigurationId),yada.loaderOn(),showServerDownloadLink(format,newFilename),void yada.ajax(gui2d.urlExportConversion+"Dxf",data,dirtyFlagTrick(),"POST",0,!0)}engine.convert(gui2d.productConfigurationName,"gltf",function(blob,filename){var data=new FormData;data.append("sourceGltfFile",blob),data.append("sourceGltfFilename",filename),data.append("productConfigurationId",gui2d.productConfigurationId),data.append("targetExtension",format),yada.loaderOn();var newFilename=filename.substr(0,filename.lastIndexOf("."))+"."+format;showServerDownloadLink(format,newFilename),yada.ajax(gui2d.urlExportConversion,data,dirtyFlagTrick(),"POST",0,!0)})}else{var outputName="Artemide_"+gui2d.productConfigurationName+"_"+(1e3+gui2d.productConfigurationId);engine.convert(outputName,format)}},gui2d.clearLocal=function(){localStorage.removeItem(getSceneDataName())},gui2d.saveLocal=function(data){null==data&&(data=engine.save());var json=JSON.stringify(data);localStorage.setItem(getSceneDataName(),json)},gui2d.loadLocal=function(){var json=localStorage.getItem(getSceneDataName());if(null!=json){var data=JSON.parse(json);engine.load(data),$("#collapse2").collapse("show")}},gui2d.findMovable=function(movableId){var movable=window.engine.getState().design.movableItems.find(function(x){return x.id==movableId});return null==movable&&console.error("No movable with id="+movableId),movable},gui2d.setMovableProperty=function(movableId,name,value){var movable=gui2d.findMovable(movableId),data=Object.assign({},movable.data);setDeepVal(data,name,value),window.engine.updateItemProperties(movableId,data),engine.setUnsaved(!0)},gui2d.setMovableItemProperty=function(movableObject,name,value){var data=Object.assign({},movableObject.data);setDeepVal(data,name,value),window.engine.updateItemProperties(movableObject.id,data),engine.setUnsaved(!0)},gui2d.onStartup=function(startupFunction){window.addEventListener("message",function(event){"config-ready"===event.data.eventType&&(registerWizardHandlers(),setTimeout(function(){startupFunction(),yada.loaderOff()},100))})},gui2d.pushArray=function(list,other){var len=other.length,start=list.length;list.length=start+len;for(var i=0;i<len;i++,start++)list[start]=other[i]},gui2d.getRequestParam=function(name){if(name=new RegExp("[?&]"+encodeURIComponent(name)+"=([^&]*)").exec(location.search))return decodeURIComponent(name[1])},gui2d.brush=function(source,target){var data=Object.assign({},source.data);engine.updateItemProperties(target.id,data)},gui2d.getCurrentInstallation=function(){return $("#wizard .typology input:checked").val()},gui2d.getPowerChoice=function(){return globalOptions.powerChoice},gui2d.openPowerSelect=function(movable,index,connected){if(globalOptions.powerChoice={movable:movable,index:index,connected:connected},1==gui2d.configuratorId)yada.ajax(gui2d.urlModalPowerA24,globalOptions.powerChoice,null,"POST",null,gui2d.hideModalLoader,!0);else if(2==gui2d.configuratorId)if(gui2d.northAmerica){gui2d.setPowerCapacity(gui2d.powerAolNorthAmerica),gui2d.setAppControlled(!1);var powerChoice=gui2d.setPowerInstallation("Ceiling");yada.ajax(gui2d.urlPowerSupplyIds,powerChoice,function(r){gui2d.finishPowerSelection(r.powerSupplyIds,r.powerSupplyIcon)},"POST",null,!0,!0)}else yada.ajax(gui2d.modalPowerAoL,globalOptions.powerChoice,null,"POST",null,gui2d.hideModalLoader,!0)},gui2d.setPowerControl=function(value){return globalOptions.powerChoice.control=value,globalOptions.powerChoice},gui2d.setPowerInstallation=function(value){return globalOptions.powerChoice.installation=value,globalOptions.powerChoice},gui2d.setPowerColor=function(value){return globalOptions.powerChoice.color=value,globalOptions.powerChoice},gui2d.setPowerCapacity=function(value){return globalOptions.powerChoice.capacity=value,globalOptions.powerChoice},gui2d.setPowerDimmable=function(value){return globalOptions.powerChoice.dimmable=1==value?"true":value,globalOptions.powerChoice},gui2d.setAppControlled=function(value){return globalOptions.powerChoice.appControlled=1==value?"true":value,globalOptions.powerChoice},gui2d.finishPowerSelection=function(productIds,powerSupplyIcon){var choices=globalOptions.powerChoice,nominal=parseInt(choices.capacity);var powerSelection={nominal:nominal,effective:2==gui2d.configuratorId?nominal:{150:"125",240:"210",320:"270"}[nominal],installation:choices.installation,color:choices.color,typology:choices.dimmable,productCode:productIds,powerSupplyIcon:powerSupplyIcon,appControlled:choices.appControlled};engine.powerSupplySelection(powerSelection,choices.movable.id,choices.index),engine.setUnsaved(!0)},gui2d.mergeMovables=function(addedMovables){for(var lastGoodKey="start",i=0;i<addedMovables.length;i++)null==addedMovables[i]?console.error("Invalid key after "+lastGoodKey):lastGoodKey=addedMovables[i].type;gui2d.pushArray(window.configuration.movables,addedMovables)},gui2d.saveTemporaryConfiguration=async function(){if(!isCacheSupported())throw"http:"===window.location.protocol&&console.error("Cache API requires HTTPS. Please use HTTPS to enable configuration saving during registration."),new Error("Cache API not supported");const scene=engine.save(),data={scene:JSON.stringify(scene),globalOptions:JSON.stringify(globalOptions),installation:gui2d.getCurrentInstallation(),configuratorId:gui2d.configuratorId,currentStep:gui2d.currentStep()};try{const cache=await caches.open("artemide-configurator"),response=new Response(JSON.stringify(data),{headers:{"Content-Type":"application/json"}});await cache.put("temporary-config",response),engine.setUnsaved(!1)}catch(error){throw console.error("Error saving temporary configuration:",error),error}},gui2d.loadTemporaryConfiguration=async function(){if(!isCacheSupported())return!1;try{const cache=await caches.open("artemide-configurator"),response=await cache.match("temporary-config");if(!response)return!1;const data=await response.json();return gui2d.loadServerData(null,data.scene,data.globalOptions,null),data.installation&&($("#wizard .typology input").prop("checked",!1),$('#wizard .typology input[value="'+data.installation+'"]').prop("checked",!0)),data.currentStep&&wizardChangeStep("#"+data.currentStep),engine.setUnsaved(!0),await async function(){if(isCacheSupported())try{const cache=await caches.open("artemide-configurator");await cache.delete("temporary-config")}catch(error){console.error("Error clearing temporary configuration:",error)}}(),!0}catch(error){return console.error("Error loading temporary configuration:",error),!1}}}(window.gui2d=window.gui2d||{});