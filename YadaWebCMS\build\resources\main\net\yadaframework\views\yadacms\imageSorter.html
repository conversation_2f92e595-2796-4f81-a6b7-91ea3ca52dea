<!DOCTYPE html>
<html xmlns:yada="http://www.yadaframework.net" xmlns:th="http://www.thymeleaf.org">
<head>
<meta charset="UTF-8">
</head>
<!--/*
A component to add, view, sort and delete a set of uploaded images.
TODO: show upload errors
Parameters:
- images = A list of images, can be empty but never null
- entityIdValue = the id of the entity holding the gallery. 
  				  It will be sent to the controller as a "id" request parameter if entityIdName is not defined.
- entityIdName = (optional) the name of the id request parameter - defaults to "id"
- multipartName = (optional) name of the multipart request parameter that will contain the uploaded image. Defaults to "newImage"
- size = YadaIntDimension for the minimum size of the image (optional)
- accept = the accept attribute (optional): file_extension, audio/*, image/* etc.
- required = true for a required value (optional)
- urlAdd - url to post the new image form. The uploaded image parameter is called "newImage"
- urlDelete - url to delete an image. It receives the "imageId" parameter.
- urlSort - url to sort an image. It receives the "currentId", "otherId" and "${entityIdName}" parameters. The controller should just 
            call yadaAttachedFileRepository.swapSortOrder(currentId, otherId);
- captions - (optional) element referring to the captions of the images - true/false. Defaults to false.
- labelCaption - label text - can be null
- urlCaption - url to modify a caption (multilanguage) - it is used if caption is present.
- jsOnReload - js to reload the caption (multilanguage) - it could be used if caption is present.
- sorterId - (optional) The id of this sorter, useful when there is more than one on page. Defaults to "yadaImageSorter"
- labelKeyAdd = key in messages.properties for the label of the upload form
- labelAdd = label to use, if not using a labelKey. When none is defined, no label is shown.
- uploadButtonKey = key in messages.properties for the upload button unescaped text (can have html for icons).
- uploadButton = upload button text to use, if not using a uploadButtonKey. Defaults to "Upload"
- deleteConfirmKey = key in messages.properties for the delete image confirmation modal.
- deleteConfirm = delete image confirmation text, if not using a deleteConfirmKey. 
				  If neither deleteConfirmKey nor deleteConfirm is given, no confirmation on delete is asked.

Example:
<div th:replace="~{/yadacms/imageSorter::component(images=${product.galleryImages},entityIdValue=${product.id},urlAdd=@{/cms/ajaxAddGalleryImage},urlDelete=@{/cms/product/deleteGalleryImage},urlSort=@{/cms/product/ajaxSortGalleryImage},labelKeyAdd='form.label.product.addGallery',deleteConfirmKey='confirm.imageGallery.delete')}"></div>


*/-->
<body>
<th:block th:fragment="component">
	<div th:if="${entityIdValue!=null}" class="yadaImageSorter" th:with="sorterId=${sorterId}?:'yadaImageSorter',entityIdName=${entityIdName}?:'id'" th:id="${sorterId}">
    	<form th:action="${urlAdd}" data-yadaUpdateOnSuccess="yadaSiblings:.yadaImageSorterImages" 
    		enctype="multipart/form-data" class="yadaAjax" method="post" role="form">
			<fieldset>
				<input type="hidden" th:name="${entityIdName}" th:value="${entityIdValue}">
				<div class="form-group">
					<label th:if="${labelAdd!=null || labelKeyAdd!=null}" th:for="|${sorterId}-newImage|" th:utext="${labelKeyAdd!=null?#messages.msg(labelKeyAdd):labelAdd}">Upload gallery image</label>
					<div th:if="${size!=null}" class="yadaMinimumSize"><span th:text="${size}">100x100</span> [[#{yada.form.fileUpload.minimumSize}]]</div>
					<input type="file" th:id="|${sorterId}-newImage|" th:name="${multipartName}?:newImage" th:accept="${accept}?:''" th:required="${required}">
				</div>
				<th:block th:if="${captions==true}">
					<div th:fragment="captions">
						<div th:each="simpleLocale,iter : ${locales}" 
							th:with="language=${#strings.substring(simpleLocale,0,2)}">
							<div class="localized" th:classappend="${language}">
								<div class="form-group has-feedback">
									<label class="control-label" th:text="${labelCaption!=null?labelCaption:''}">Caption</label>
									<div class="input-group col">
									  <div class="input-group-prepend" th:text="${#strings.toUpperCase(language)}">IT</div>
									  <input type="text" th:name="title[__${simpleLocale}__]" th:value="${galleryImage!=null?galleryImage.title[__${simpleLocale}__]:''}" class="form-control  has-feedback"
								  		aria-describedby="caption image">
								    </div>
							    </div>
							</div>
						</div>
					</div>
				</th:block>
				<button class="btn btn-success" type="submit" name="Upload" th:utext="${uploadButtonKey!=null?#messages.msg(uploadButtonKey):uploadButton?:'Upload'}">Upload</button>
			</fieldset>
		</form>

		<form th:action="${urlSort}" data-yadaUpdateOnSuccess="yadaSiblings:.yadaImageSorterImages" yada:successHandler="${jsOnReload}" class="yadaAjax sortForm yadaNoLoader">
			<input type="hidden" th:name="${entityIdName}" th:value="${entityIdValue}">
		</form>
		<form th:action="${urlDelete}" data-yadaUpdateOnSuccess="yadaSiblings:.yadaImageSorterImages" class="yadaAjax deleteForm yadaNoLoader"
			yada:confirm="${deleteConfirmKey!=null?#messages.msg(deleteConfirmKey):deleteConfirm?:''}">
			<input type="hidden" th:name="${entityIdName}" th:value="${entityIdValue}">
		</form>
		<div class="yadaImageSorterImages">
			<th:block th:if="${messages!=null || errorMessages!=null}">
				<div th:replace="/yada/feedbackMessages :: div"></div>
			</th:block>
			<div th:each="galleryImage,iter : ${images}" class="yadaImageControl">
				<div class="row yadaUploadedImages" th:with="colClassPrefix=${@config.getForB3B4B5('col-xs','col','col')}">
					<div th:if="${galleryImage.desktopImage}" th:class="${galleryImage.mobileImage}?${colClassPrefix}+'-8':${colClassPrefix}+'-12'">
						<a th:href="${@yadaFileManager.getDesktopImageUrl(galleryImage)}" target="_blank">
							<img class="img-responsive" th:src="@{${@yadaFileManager.getDesktopImageUrl(galleryImage)}}">
						</a>
						<div>[[${galleryImage.desktopImageDimension}]]</div>
						
						<th:block th:if="${captions==true}">
							<form class="yadaAjax captionForm yadaNoLoader" th:object="${galleryImage}"
								th:action="${urlCaption}" data-yadaUpdateOnSuccess="yadaSiblingsFind:.message span">
								<!-- data-yadaParentForm="yadaClosestFind:.yadaImageSorter .captionForm"> -->
								<input type="hidden" th:name="${entityIdName}" th:value="${entityIdValue}">
								<input type="hidden" name="yadaAttachedFileId" th:value="${galleryImage.id}">
								<div th:replace="~{::captions}"></div>
								<br/>
								<button class="btn btn-success" type="submit" name="captionUpdate">Update</button>
							</form>
							<div class="message">
								<span></span>
							</div>
						</th:block>
						
					</div>
					<div th:if="${galleryImage.mobileImage}" th:class="${galleryImage.desktopImage}?${colClassPrefix}+'-4':${colClassPrefix}+'-12'">
						<a th:href="${@yadaFileManager.getMobileImageUrl(galleryImage)}" target="_blank">
							<img class="img-responsive" th:src="@{${@yadaFileManager.getMobileImageUrl(galleryImage)}}">
						</a>
						<div>[[${galleryImage.mobileImageDimension}]]</div>
					</div>
				</div>
			
				<div class="commandBar">
					<!--/* Move up */-->
					<form th:unless="${iter.last}" th:action="${urlSort}" data-yadaParentForm="yadaClosestFind:.yadaImageSorter .sortForm" >
						<input type="hidden" name="currentId" th:value="${galleryImage.id}">
						<input type="hidden" name="otherId" th:value="${images.get(iter.index+1).id}">
						<a href="#" onclick="$(this).closest('form').submit(); return false;">
							<i class="moveup fa fa-chevron-down" aria-hidden="true"></i>
						</a>
					</form>
					<!--/* Move down */-->
					<form th:unless="${iter.first}" data-yadaParentForm="yadaClosestFind:.yadaImageSorter .sortForm" >
						<input type="hidden" name="currentId" th:value="${galleryImage.id}">
						<input type="hidden" name="otherId" th:value="${images.get(iter.index-1).id}">
						<a href="#" onclick="$(this).closest('form').submit(); return false;">
							<i class="moveup fa fa-chevron-up" aria-hidden="true"></i>
						</a>
					</form>
					<!--/* Delete */-->
					<form th:action="${urlDelete}" data-yadaParentForm="yadaClosestFind:.yadaImageSorter .deleteForm">
						<input type="hidden" name="imageId" th:value="${galleryImage.id}">
						<a href="#" onclick="$(this).closest('form').submit(); return false;" th:classappend="${deleteConfirmKey!=null || deleteConfirm!=null} ? yadaNoLoader">
							<i class="delete yadaIcon yadaIcon-delete" aria-hidden="true"></i>
						</a>
					</form>
				</div>
				<hr>
			</div>
		</div>
	</div>

</th:block>    	
</body>
</html>

