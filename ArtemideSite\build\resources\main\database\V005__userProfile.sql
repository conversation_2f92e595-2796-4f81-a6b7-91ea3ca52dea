# Store favourites in the UserProfile
#

create table YadaUserProfile_Famiglia (UserProfile_id bigint not null, favouriteFamilies_id bigint not null) engine=InnoDB;
create table YadaUserProfile_Project (UserProfile_id bigint not null, favouriteProjects_id bigint not null) engine=InnoDB;
create table YadaUserProfile_Subfamily (UserProfile_id bigint not null, favouriteSubfamilies_id bigint not null) engine=InnoDB;
alter table YadaUserProfile add column DTYPE varchar(31) not null;

alter table YadaUserProfile_Famiglia add constraint FK9kn5uv12oc8oym2ocvqi7i82h foreign key (favouriteFamilies_id) references Famiglia (id);
alter table YadaUserProfile_Famiglia add constraint FKptb4x11aqinw5othd7b4hxbb5 foreign key (UserProfile_id) references YadaUserProfile (id);
alter table YadaUserProfile_Project add constraint FKhnd9wyhafswdvdkbqcjt8vu56 foreign key (favouriteProjects_id) references Project (id);
alter table YadaUserProfile_Project add constraint FKm8tml8nnjxh3h5h1iyldkbvti foreign key (UserProfile_id) references YadaUserProfile (id);
alter table YadaUserProfile_Subfamily add constraint FKsorlk19uovva6uwc3q2us33il foreign key (favouriteSubfamilies_id) references Subfamily (id);
alter table YadaUserProfile_Subfamily add constraint FKrxv5knw34hfe6jq972jmxh06x foreign key (UserProfile_id) references YadaUserProfile (id);
