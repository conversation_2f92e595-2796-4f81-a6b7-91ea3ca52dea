# Remove the article without sap code
delete from ArticoloDescrizione where Articolo_id=4656909;
delete from PrezzoSap where Articolo_id=4656909;
delete from Articolo where id=4656909;

# Add the new types and constraints
# LocalString used to be a @Lob implemented as TINYTEXT, now converted to TEXT
ALTER TABLE Articolo MODIFY codiceSap varchar(255) not null;
ALTER TABLE ArticoloDescrizione MODIFY value TEXT;
ALTER TABLE CustomButtonLink MODIFY value TEXT;
ALTER TABLE CustomButtonName MODIFY value TEXT;
ALTER TABLE Designer MODIFY surname varchar(50) not null;
ALTER TABLE DesignerDescription MODIFY value TEXT;
ALTER TABLE ElectricalName MODIFY value TEXT;
# FamigliaNome is useless and is not in the db
# ALTER TABLE FamigliaNome MODIFY value TEXT;
ALTER TABLE FilterVarianteDescription MODIFY value TEXT;
ALTER TABLE GalleryDescriptionText MODIFY value TEXT;
ALTER TABLE PdfData MODIFY frontView longtext;
ALTER TABLE PdfData MODIFY jsonConnections longtext;
ALTER TABLE PdfData MODIFY jsonPowerOptions longtext;
ALTER TABLE PdfData MODIFY perspectiveView longtext;
ALTER TABLE PdfData MODIFY sideView longtext;
ALTER TABLE PdfData MODIFY topView longtext;
ALTER TABLE Prodotto MODIFY codice varchar(255) not null;
ALTER TABLE ProdottoBallast MODIFY value TEXT;
ALTER TABLE ProdottoBaseColor MODIFY value TEXT;
ALTER TABLE ProdottoColor MODIFY value TEXT;
ALTER TABLE ProdottoDescription MODIFY value TEXT;
ALTER TABLE ProdottoElectricalBallast MODIFY value TEXT;
ALTER TABLE ProdottoElectricalEmergency MODIFY value TEXT;
ALTER TABLE ProdottoElectricalName MODIFY value TEXT;
ALTER TABLE ProdottoElectricalRemoteControl MODIFY value TEXT;
ALTER TABLE ProdottoElectricalTransformer MODIFY value TEXT;
ALTER TABLE ProdottoEmergency MODIFY value TEXT;
ALTER TABLE ProdottoMaterial MODIFY value TEXT;
ALTER TABLE ProdottoName MODIFY value TEXT;
ALTER TABLE ProdottoNote MODIFY value TEXT;
ALTER TABLE ProdottoRemoteControl MODIFY value TEXT;
ALTER TABLE ProdottoShortName MODIFY value TEXT;
ALTER TABLE ProdottoSubName MODIFY value TEXT;
ALTER TABLE ProdottoTransformer MODIFY value TEXT;
ALTER TABLE ProductConfiguration MODIFY globalOptions longtext;
ALTER TABLE ProductConfiguration MODIFY scene longtext;
ALTER TABLE Property MODIFY value longtext;
ALTER TABLE PublicationsLink MODIFY value TEXT;
ALTER TABLE PublicationsSubtitle MODIFY value TEXT;
ALTER TABLE PublicationsTitle MODIFY value TEXT;
ALTER TABLE Store MODIFY nome varchar(255) not null;
ALTER TABLE SubfamilyConfigText MODIFY value TEXT;
ALTER TABLE SubfamilyCutoutShape MODIFY value TEXT;
ALTER TABLE SubfamilyEmission MODIFY value TEXT;
ALTER TABLE SubfamilyEnvironment MODIFY value TEXT;
ALTER TABLE SubfamilyName MODIFY value TEXT;
ALTER TABLE UploadedFileDescription MODIFY value TEXT;
ALTER TABLE UploadedFileTitle MODIFY value TEXT;
ALTER TABLE VarianteDescription MODIFY value TEXT;
ALTER TABLE YadaClause MODIFY content longtext;
ALTER TABLE PageModule ADD CONSTRAINT PageModule_DTYPE_check CHECK (DTYPE in ('PageModule','HomeModule','NewsModule','ProjectModule'));
ALTER TABLE PageModule ADD CONSTRAINT PageModule_ProjectModule_year_check CHECK (DTYPE <> 'ProjectModule' or (year is not null));
ALTER TABLE Tag ADD CONSTRAINT Tag_DTYPE_check CHECK (DTYPE in ('Tag','NewsTag','ProjectTag'));
ALTER TABLE YadaUserProfile ADD CONSTRAINT YadaUserProfile_DTYPE_check CHECK (DTYPE in ('YadaUserProfile','UserProfile'));