# Product files belonging to subfamilies

create table ProductFile (type tinyint check (type between 0 and 12), attachedFile_id bigint, id bigint not null auto_increment, subfamily_id bigint, timestamp TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP null, version bigint not null, primary key (id)) engine=InnoDB;
create table ProductFile_GalleryImage (galleryImage_id bigint not null, productfile_id bigint not null, primary key (galleryImage_id, productfile_id)) engine=InnoDB;
create table ProductFile_Prodotto (prodotto_id bigint not null, productfile_id bigint not null, primary key (prodotto_id, productfile_id)) engine=InnoDB;

alter table ProductFile add constraint UK_c2jvhpxpbn327enk3bwvf0yyc unique (attachedFile_id);
alter table ProductFile_GalleryImage add constraint UK_5m22dm8wvsrj0i6ne7f283ra9 unique (galleryImage_id);

alter table ProductFile add constraint FKbc52uo3i0i58418028io1mtcf foreign key (attachedFile_id) references YadaAttachedFile (id);
alter table ProductFile add constraint FKlbpjtwgdweyi0m8nhalrxe3dq foreign key (subfamily_id) references Subfamily (id);
alter table ProductFile_GalleryImage add constraint FKda0ugbkvtgk1qh87gv34rrcfg foreign key (galleryImage_id) references YadaAttachedFile (id);
alter table ProductFile_GalleryImage add constraint FK93h1g61idr3o3iiqngpxxhfh5 foreign key (productfile_id) references ProductFile (id);
alter table ProductFile_Prodotto add constraint FKqamds506t1m5myb16wgp14dbm foreign key (prodotto_id) references Prodotto (id);
alter table ProductFile_Prodotto add constraint FKirwf9mal94xsombo6klyqu6rs foreign key (productfile_id) references ProductFile (id);

