## Creazioni tabelle per la HomePageGallery

create table HomeGallerySubtitle (id bigint not null auto_increment, localeCode varchar(5), value longtext, HomePageGallery_id bigint, primary key (id)) engine=InnoDB;
create table HomeGalleryTitle (id bigint not null auto_increment, localeCode varchar(5), value longtext, HomePageGallery_id bigint, primary key (id)) engine=InnoDB;
create table HomePageGallery (id bigint not null auto_increment, pos integer not null, photographer varchar(255), link varchar(255), homePage_id bigint, primary key (id)) engine=InnoDB;
create table HomePageGallery_SortedUploadedFiles (HomePageGallery_id bigint not null, sortedUploadedFilesMap_id bigint not null, sortedUploadedFilesMap_KEY varchar(255) not null, primary key (HomePageGallery_id, sortedUploadedFilesMap_KEY)) engine=InnoDB;
alter table HomePageGallery_SortedUploadedFiles add constraint UK_260qc8jxs2wthwraejj77lwfg unique (sortedUploadedFilesMap_id);
alter table HomeGallerySubtitle add constraint FKqgocodyyy5y94nvjkqqt71nkt foreign key (HomePageGallery_id) references HomePageGallery (id);
alter table HomeGalleryTitle add constraint FKp2vrxf1m819e5rl598nrr36j2 foreign key (HomePageGallery_id) references HomePageGallery (id);
alter table HomePageGallery add constraint FK5o5i9mdhoew2e75rqo4np0b1d foreign key (homePage_id) references HomePage (id);
alter table HomePageGallery_SortedUploadedFiles add constraint FK1j38l9fjuk9if411digsqd3ay foreign key (sortedUploadedFilesMap_id) references SortedUploadedFiles (id);
alter table HomePageGallery_SortedUploadedFiles add constraint FK95ao5gw4ntiloshlxisbr1b15 foreign key (HomePageGallery_id) references HomePageGallery (id);
