## Aggiunta nuove tabelle per Publications per Pagina Download

create table Publications (id bigint not null auto_increment, typology integer, primary key (id)) engine=InnoDB;
create table Publications_SortedUploadedFiles (Publications_id bigint not null, sortedUploadedFilesMap_id bigint not null, sortedUploadedFilesMap_KEY varchar(255) not null, primary key (Publications_id, sortedUploadedFilesMap_KEY)) engine=InnoDB;
create table PublicationsLink (id bigint not null auto_increment, localeCode varchar(5), value longtext, Publications_id bigint, primary key (id)) engine=InnoDB;
create table PublicationsSubtitle (id bigint not null auto_increment, localeCode varchar(5), value longtext, Publications_id bigint, primary key (id)) engine=InnoDB;
create table PublicationsTitle (id bigint not null auto_increment, localeCode varchar(5), value longtext, Publications_id bigint, primary key (id)) engine=InnoDB;
