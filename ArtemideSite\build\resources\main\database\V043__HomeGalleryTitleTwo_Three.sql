## Aggiunta nuove tabelle HomeGalleryTitleTwo e Three per Remake 2019

create table HomeGalleryTitleThree (id bigint not null auto_increment, localeCode varchar(5), value longtext, HomePageGallery_id bigint, primary key (id)) engine=InnoDB;
create table HomeGalleryTitleTwo (id bigint not null auto_increment, localeCode varchar(5), value longtext, HomePageGallery_id bigint, primary key (id)) engine=InnoDB;
alter table HomeGalleryTitleThree add constraint FK2a00c4b4faih1023od0lw6rp9 foreign key (HomePageGallery_id) references HomePageGallery (id);
alter table HomeGalleryTitleTwo add constraint FK54qtb3yccnq3nv8pvs8qu1qd4 foreign key (HomePageGallery_id) references HomePageGallery (id);