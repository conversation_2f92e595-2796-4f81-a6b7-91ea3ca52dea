# Aggiunge campi alla YadaRegistrationRequest e a YadaUserProfile per gestire la newsletter
create table AmdRegistrationRequest (name var<PERSON><PERSON>(255), newsletterFlag bit, newsletterFlagDate datetime, surname varchar(255), id bigint not null, primary key (id)) engine=InnoDB;

ALTER TABLE YadaUserProfile ADD newsletterFlag bit;
ALTER TABLE YadaUserProfile ADD newsletterFlagDate datetime;

alter table AmdRegistrationRequest add constraint FKclmnok8v3h1otw59bk89vf2vi foreign key (id) references YadaRegistrationRequest (id);