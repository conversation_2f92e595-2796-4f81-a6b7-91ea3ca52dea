SET FOREIGN_KEY_CHECKS = 0;

truncate table EventoNews;

SET FOREIGN_KEY_CHECKS = 1;

alter table EventoNews drop column title;

alter table EventoNews drop column language;

create table NewsContent (id bigint not null auto_increment, localeCode varchar(5), value longtext, EventoNews_id bigint, primary key (id)) ENGINE=InnoDB;
create table NewsTitle (id bigint not null auto_increment, localeCode varchar(5), value longtext, EventoNews_id bigint, primary key (id)) ENGINE=InnoDB;
alter table NewsContent add index FKF08EA606D5FA2A9D (EventoNews_id), add constraint FKF08EA606D5FA2A9D foreign key (EventoNews_id) references EventoNews (id);
alter table NewsTitle add index FK88C49325D5FA2A9D (EventoNews_id), add constraint FK88C49325D5FA2A9D foreign key (EventoNews_id) references EventoNews (id);