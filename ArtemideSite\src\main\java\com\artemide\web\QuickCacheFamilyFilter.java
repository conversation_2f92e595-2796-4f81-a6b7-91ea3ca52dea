package com.artemide.web;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Locale;
import java.util.concurrent.Callable;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.Executor;
import java.util.concurrent.TimeUnit;

import jakarta.annotation.PostConstruct;

import org.apache.commons.lang3.LocaleUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.MessageSource;
import org.springframework.stereotype.Component;

import com.artemide.common.AmdConfiguration;
import com.artemide.common.components.AmdMasonry;
import com.artemide.common.components.AmdUtil;
import com.artemide.common.repository.DesignerRepository;
import com.artemide.common.repository.FamigliaDao;
import com.artemide.common.repository.FamigliaFilterResult;
import com.artemide.common.repository.FamilyFilterDao;
import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.LoadingCache;
import com.google.common.util.concurrent.ListenableFuture;
import com.google.common.util.concurrent.ListenableFutureTask;
import com.yr.entity.Designer;
import com.yr.entity.localstring.FamigliaNome;

import net.yadaframework.exceptions.YadaInvalidUsageException;
import net.yadaframework.web.YadaPageRequest;

/**
 * Cache temporanea per valori che cambiano di rado e che non occupano troppa memoria.
 * Docs: https://github.com/google/guava/wiki/CachesExplained
 * NOTA: questa implementazione è inutilmente complessa. 
 * Vedi {@link com.artemide.web.QuickCacheSubfamily QuickCacheSubfamily} per un'implementazione migliore.
 *
 */
@Component
public class QuickCacheFamilyFilter {
	private final transient Logger log = LoggerFactory.getLogger(getClass());

	@Autowired private FamigliaDao famigliaDao;
	@Autowired private Executor taskExecutor;
	@Autowired private MessageSource messageSource;
	// @Autowired private UploadedAttributesDao<Famiglia> uploadedAttributesDaoFamily;
	@Autowired private AmdConfiguration config;
	@Autowired private DesignerRepository designerRepository;
	@Autowired private FamilyFilterDao familyFilterDao;

	private LoadingCache<String, Object> cache;

	// The cache will try to evict entries that haven't been used recently or very often:
	// https://github.com/google/guava/wiki/CachesExplained#size-based-eviction
	private static final long CACHESIZE = 1000;	// se tiene i puntatori, occupa circa 4k in RAM

	private final static String OBJECTNAME_FILTERED_FAMILYANDNAMES = "filteredFamilyIdAndNames";
	private final static String OBJECTNAME_FILTERED_FAMILYDATA = "filteredFamilyData";
	private final static String OBJECTNAME_ALL_DESIGNERS = "allDesigners";
	private final static String OBJECTNAME_FILTERED_CATALOGS = "filteredCatalogs";
	private final static String OBJECTNAME_FILTERED_TYPOLOGIES = "filteredTypologies";
	private final static String OBJECTNAME_FILTERED_DESIGNERS = "filteredDesigners";
	private final static String OBJECTNAME_FILTERED_LAMPCATEGORIES = "filteredLampCategories";
	private final static String OBJECTNAME_DESIGNER_STRING = "designerString";

	private static final char KEY_SEPARATOR_1 = ','; // Key section separator
	private static final char KEY_SEPARATOR_2 = ';'; // Key subsection separator
	private static final String KEY_SEPARATOR_1_STR = String.valueOf(KEY_SEPARATOR_1);
	private static final String KEY_SEPARATOR_2_STR = String.valueOf(KEY_SEPARATOR_2);

	public class DesignerData {
		public Long id;
		public String text;
//		public List<Designer> designers = new ArrayList<>();
//		public List<Long> designerIds = new ArrayList<>();
	}

	private class FilterInputData {
		public List<Integer> series = new ArrayList<>();
		public List<Integer> typologies = new ArrayList<>();
		public List<String> lampCategories = new ArrayList<>();
		public List<Long> designerIds = new ArrayList<>();
		public Locale locale = null;
		public YadaPageRequest yadaPageRequest = null;
		public List<AmdMasonry.ImageSize> lastCells = new ArrayList<>(); // Usato da AmdMasonry per memorizzare le celle già occupate dalla pagina precedente
		private boolean appOnly = false;
	}

	private Object loadFromDatabase(String key) {
		log.debug("Loading cache content from database for key {}", key);
		String[] parts = key.split(KEY_SEPARATOR_1_STR, 2);
		String objectName = parts[0];
		switch (objectName) {
//		case OBJECTNAME_DESIGNER_STRING:
//			return loadDesignerString(key);
		case OBJECTNAME_DESIGNER_STRING:
			return loadDesignerString(key);
		case OBJECTNAME_FILTERED_DESIGNERS:
			return loadFilteredDesigners(key);
		case OBJECTNAME_FILTERED_LAMPCATEGORIES:
			return loadFilteredLampCategories(key);
		case OBJECTNAME_FILTERED_TYPOLOGIES:
			return loadFilteredTypologyIds(key);
		// availableCatalogs not needed anymore
		//	case OBJECTNAME_FILTERED_CATALOGS:
		//		return loadFilteredCatalogs(key);
		case OBJECTNAME_ALL_DESIGNERS:
			return loadDesignerData();
		case OBJECTNAME_FILTERED_FAMILYANDNAMES:
			return loadFamilyAndNames(key);
		case OBJECTNAME_FILTERED_FAMILYDATA:
			return loadFamilyData(key);
		default:
			break;
		}
		throw new YadaInvalidUsageException("Invalid key value '{}' for QuickCache", key);
	}

	/**
	 * Crea la chiave per la cache. E' composta da tre sezioni separate da , e ogni sezione contiene gli id separati da ;
	 * @param objectName
	 * @param series
	 * @param typologies
	 * @param lampCategories
	 * @param designerIds
	 * @param appOnly
	 * @return una stringa come filteredCatalogs,0;7,2;4;3,HALO;FLUO;LED,4352;54366;24323;2342342
	 */
	private String makeFilteredCatalogsKey(String objectName, List<Integer> series, List<Integer> typologies, List<String> lampCategories, List<Long> designerIds, boolean appOnly) {
		StringBuilder result = new StringBuilder(objectName).append(KEY_SEPARATOR_1);
		for (Integer val : series) {
			result.append(val).append(KEY_SEPARATOR_2);
		}
		deleteIfLastChar(result, KEY_SEPARATOR_2);
		result.append(KEY_SEPARATOR_1);
		for (Integer val : typologies) {
			result.append(val).append(KEY_SEPARATOR_2);
		}
		deleteIfLastChar(result, KEY_SEPARATOR_2);
		result.append(KEY_SEPARATOR_1);
		for (String val : lampCategories) {
			result.append(val).append(KEY_SEPARATOR_2);
		}
		deleteIfLastChar(result, KEY_SEPARATOR_2);
		result.append(KEY_SEPARATOR_1);
		for (Long val : designerIds) {
			result.append(val).append(KEY_SEPARATOR_2);
		}
		deleteIfLastChar(result, KEY_SEPARATOR_2);
		result.append(KEY_SEPARATOR_1);
		result.append(appOnly);
		return result.toString();
	}

	/**
	 * Converte una key agli oggetti da cui è tratta
	 * @param key
	 * @return
	 */
	public FilterInputData unmakeFilterKey(String key) {
		FilterInputData result = new FilterInputData();
		String[] parts1 = key.split(KEY_SEPARATOR_1_STR, 9); // [filteredCatalogs, 0;7, 2;4;3, HALO;FLUO;LED, 4352;54366;24323;2342342, true, it_IT, 2;8;false, 1;2;3]
		// String objectName = parts[0]; // Ignoriamo objectName
		String seriesString = parts1[1];
		String typologiesString = parts1[2];
		String lampCategoriesString = parts1[3];
		String designerIdsString = parts1[4];
		String appOnlyString = parts1[5];
		String localeString = parts1.length>6?parts1[6]:null;
		String pageString = parts1.length>7?parts1[7]:null;
		String lastCellsString = parts1.length>8?parts1[8]:null;
		if (StringUtils.trimToNull(seriesString)!=null) {
			for (String serieString : seriesString.split(KEY_SEPARATOR_2_STR)) {
				result.series.add(Integer.parseInt(serieString));
			}
		}
		if (StringUtils.trimToNull(typologiesString)!=null) {
			for (String typologyString : typologiesString.split(KEY_SEPARATOR_2_STR)) {
				result.typologies.add(Integer.parseInt(typologyString));
			}
		}
		if (StringUtils.trimToNull(lampCategoriesString)!=null) {
			result.lampCategories = Arrays.asList(lampCategoriesString.split(KEY_SEPARATOR_2_STR));
		}
		if (StringUtils.trimToNull(designerIdsString)!=null) {
			for (String designerIdString : designerIdsString.split(KEY_SEPARATOR_2_STR)) {
				result.designerIds.add(Long.parseLong(designerIdString));
			}
		}
		result.appOnly = "true".equals(appOnlyString);
		if (StringUtils.trimToNull(localeString)!=null) {
			result.locale = LocaleUtils.toLocale(localeString);
		}
		if (StringUtils.trimToNull(pageString)!=null) {
			String[] parts = pageString.split(KEY_SEPARATOR_2_STR);
			int pageNumber = Integer.parseInt(parts[0]);
			int pageSize = Integer.parseInt(parts[1]);
			boolean loadPrevious = Boolean.parseBoolean(parts[2]);
			result.yadaPageRequest = new YadaPageRequest(pageNumber, pageSize, loadPrevious);
		}
		if (StringUtils.trimToNull(lastCellsString)!=null) {
			for (String lastCell : lastCellsString.split(KEY_SEPARATOR_2_STR)) {
				AmdMasonry.ImageSize value = StringUtils.trimToNull(lastCell)==null?null:AmdMasonry.ImageSize.valueOf(lastCell);
				result.lastCells.add(value); // [BIG, SMALL, null]
			}
		}
		return result;
	}

	private void deleteIfLastChar(StringBuilder value, char toRemoveIfLast) {
		int lastCharPos = value.length()-1;
		if (value.charAt(lastCharPos)==toRemoveIfLast) {
			value.deleteCharAt(lastCharPos);
		}
	}


	///////////////////////////////////////////////////////////
	// Designer text (toString())

	public String getDesignerToString(Long designerId) {
		String key = String.format("%s,%s", OBJECTNAME_DESIGNER_STRING, designerId);
		if (AmdUtil.isPreview()) {
			return (String) loadDesignerString(key); // in preview bypasso la cache
		}
		try {
			// Se non siamo in preview prelevo dalla cache, che eventualmente carica da db se il contenuto manca o è vecchio
			return (String) cache.get(key);
		} catch (ExecutionException e) {
			log.error("Errore prelevando i Designer da db (ignorato)", e);
		}
		return "";
	}

	private Object loadDesignerString(String key) {
		String[] parts = key.split(",");
		Long id = Long.parseLong(parts[1]);
		// Prendo il designer
		Designer designer = designerRepository.findById(id).orElse(null);
		return designer.toString();
	}

	///////////////////////////////////////////////////////////
	// Filtered Designers

	@SuppressWarnings("unchecked")
	public List<Long> getFilteredDesigners(List<Integer> series, List<Integer> typologies, List<String> lampCategories, boolean appOnly) {
		String key = makeFilteredCatalogsKey(OBJECTNAME_FILTERED_DESIGNERS, series, typologies, lampCategories, new ArrayList<Long>(), appOnly);
		// Teniamo in cache solo quando per ogni filtro ci sono al massimo 4 selezioni
		if (AmdUtil.isPreview() || series.size()>4 || typologies.size()>4 || lampCategories.size()>4) {
			return (List<Long>) loadFilteredDesigners(key); // bypasso la cache
		}
		try {
			return (List<Long>) cache.get(key);
		} catch (ExecutionException e) {
			log.error("Errore prelevando i FilteredTypologyIds da cache (ignorato)", e);
		}
		return new ArrayList<>();
	}

	private Object loadFilteredDesigners(String key) {
		FilterInputData data = unmakeFilterKey(key);
		return familyFilterDao.getFilteredDesigners(data.series, data.typologies, data.lampCategories, data.appOnly);
	}

	///////////////////////////////////////////////////////////
	// Filtered Lamp Categories

	@SuppressWarnings("unchecked")
	public List<String> getFilteredLampCategories(List<Integer> series, List<Integer> typologies, List<Long> designerIds, boolean appOnly) {
		String key = makeFilteredCatalogsKey(OBJECTNAME_FILTERED_LAMPCATEGORIES, series, typologies, new ArrayList<String>(), designerIds, appOnly);
		// Teniamo in cache solo quando per ogni filtro ci sono al massimo 4 selezioni
		if (AmdUtil.isPreview() || series.size()>4 || typologies.size()>4 || designerIds.size()>4) {
			return (List<String>) loadFilteredLampCategories(key); // bypasso la cache
		}
		try {
			return (List<String>) cache.get(key);
		} catch (ExecutionException e) {
			log.error("Errore prelevando i FilteredLampCategories da cache (ignorato)", e);
		}
		return new ArrayList<>();
	}

	private Object loadFilteredLampCategories(String key) {
		FilterInputData data = unmakeFilterKey(key);
		return familyFilterDao.getFilteredLampCategories(data.series, data.typologies, data.designerIds, data.appOnly);
	}

	///////////////////////////////////////////////////////////
	// Filtered Typology

	@SuppressWarnings("unchecked")
	public List<Integer> getFilteredTypologyIds(List<Integer> series, List<String> lampCategories, List<Long> designerIds, boolean appOnly) {
		String key = makeFilteredCatalogsKey(OBJECTNAME_FILTERED_TYPOLOGIES, series, new ArrayList<Integer>(), lampCategories, designerIds, appOnly);
		// Teniamo in cache solo quando per ogni filtro ci sono al massimo 4 selezioni
		if (AmdUtil.isPreview() || series.size()>4 || lampCategories.size()>4 || designerIds.size()>4) {
			return (List<Integer>) loadFilteredTypologyIds(key); // bypasso la cache
		}
		try {
			return (List<Integer>) cache.get(key);
		} catch (ExecutionException e) {
			log.error("Errore prelevando i FilteredTypologyIds da cache (ignorato)", e);
		}
		return new ArrayList<>();
	}

	private Object loadFilteredTypologyIds(String key) {
		FilterInputData data = unmakeFilterKey(key);
		return familyFilterDao.getFilteredTypologies(data.series, data.lampCategories, data.designerIds, data.appOnly);
	}


	///////////////////////////////////////////////////////////
	// Filtered Catalogs

	// availableCatalogs not needed anymore
	//	@SuppressWarnings("unchecked")
	//	public List<Integer> getFilteredCatalogs(List<Integer> typologies, List<String> lampCategories, List<Long> designerIds, boolean appOnly) {
	//		String key = makeFilteredCatalogsKey(OBJECTNAME_FILTERED_CATALOGS, new ArrayList<Integer>(), typologies, lampCategories, designerIds, appOnly);
	//		// Teniamo in cache solo quando per ogni filtro ci sono al massimo 4 selezioni
	//		if (AmdUtil.isPreview() || typologies.size()>4 || lampCategories.size()>4 || designerIds.size()>4) {
	//			return (List<Integer>) loadFilteredCatalogs(key); // bypasso la cache
	//		}
	//		try {
	//			return (List<Integer>) cache.get(key);
	//		} catch (ExecutionException e) {
	//			log.error("Errore prelevando i FilteredCatalogs da cache (ignorato)", e);
	//		}
	//		return new ArrayList<>();
	//	}

	// availableCatalogs not needed anymore
	//	private Object loadFilteredCatalogs(String key) {
	//		FilterInputData data = unmakeFilterKey(key);
	//		return familyFilterDao.getFilteredCatalogs(data.typologies, data.lampCategories, data.designerIds, data.appOnly);
	//	}


	///////////////////////////////////////////////////////////
	// Family Data

	/**
	 *
	 * @param series
	 * @param typologies
	 * @param lampCategories
	 * @param designerIds
	 * @param locale
	 * @param pageable
	 * @param lastCells a string like "BIG;SMALL;SMALL" with the family images of the previous last line of the page
	 * @param appOnly
	 * @return
	 */
	public FamigliaFilterResult getFilteredFamilyData(List<Integer> series, List<Integer> typologies, List<String> lampCategories, List<Long> designerIds, Locale locale, YadaPageRequest yadaPageRequest, String lastCells, boolean appOnly) {
		// La key è tipo filteredCatalogs,0;7,2;4;3,HALO;FLUO;LED,4352;54366;24323;2342342,it_IT,2;8
		String semiKey = makeFilteredCatalogsKey(OBJECTNAME_FILTERED_FAMILYDATA, series, typologies, lampCategories, designerIds, appOnly);
		String key = String.format("%s,%s,%s;%s;%s,%s", semiKey, locale, yadaPageRequest.getPage(), yadaPageRequest.getSize(), yadaPageRequest.isLoadPrevious(), lastCells);
		if (AmdUtil.isPreview() || typologies.size()>4 || lampCategories.size()>4 || designerIds.size()>4) {
			return (FamigliaFilterResult) loadFamilyData(key);
		}
		try {
			return (FamigliaFilterResult) cache.get(key);
		} catch (ExecutionException e) {
			log.error("Errore prelevando da cache le famiglie corrispondenti al filtro (ignorato)", e);
		}
		return new FamigliaFilterResult(); // TODO should this be "return loadFamilyData(key)" instead?
	}

	private Object loadFamilyData(String key) {
		FilterInputData data = unmakeFilterKey(key);
		return familyFilterDao.getFilteredFamilyData(data.series, data.typologies, data.lampCategories, data.designerIds, data.appOnly, data.locale, data.yadaPageRequest, data.lastCells);
	}

	///////////////////////////////////////////////////////////
	// Designer Data

	public List<DesignerData> getAllDesignerData() {
		String key = OBJECTNAME_ALL_DESIGNERS;
		if (AmdUtil.isPreview()) {
			return (List<DesignerData>) loadDesignerData(); // in preview bypasso la cache
		}
		try {
			// Se non siamo in preview prelevo dalla cache, che eventualmente carica da db se il contenuto manca o è vecchio
			return (List<DesignerData>) cache.get(key);
		} catch (ExecutionException e) {
			log.error("Errore prelevando i Designer da cache (ignorato)", e);
		}
		return new ArrayList<>();
	}

	private Object loadDesignerData() {
		// Prendo i designer
		List<Designer> designers = designerRepository.getAllPublished(AmdUtil.isPreview());
		// Raccolgo solo gli id e i toString
		List<DesignerData> result = new ArrayList<>();
		for (Designer designer : designers) {
			DesignerData designerData = new DesignerData();
			result.add(designerData);
			designerData.id = designer.getId();
			designerData.text = designer.toString();
		}
		return result;
	}

	///////////////////////////////////////////////////////////
	// Family and names

	/**
	 * @see #getFamilyAndNames(String, Locale)
	 * @param series
	 * @param locale
	 * @return
	 */
	public List<String[]> getFamilyAndNames(int series, Locale locale) {
		String seriesString = series==-1?"":Integer.toString(series);
		return getFamilyAndNames(seriesString, locale);
	}

	/**
	 * Ritorna il valore in cache della tendina famiglie nella sezione prodotti. Se non è presente, viene calcolato.
	 * Se siamo in preview, bypassa la cache.
	 * @param series il catalogo (0=Design, etc.)
	 * @param locale
	 * @return Lista di array contenente [id, nome]
	 */
	@SuppressWarnings("unchecked")
	public List<String[]> getFamilyAndNames(String series, Locale locale) {
		String key = String.format("%s,%s,%s", OBJECTNAME_FILTERED_FAMILYANDNAMES, series, locale);
		if (AmdUtil.isPreview()) {
			return (List<String[]>) loadFamilyAndNames(key);
		}
		try {
			return (List<String[]>) cache.get(key);
		} catch (ExecutionException e) {
			log.error("Errore prelevando il valore della tendina famiglie (ignorato)", e);
		}
		return new ArrayList<>();
	}

	/**
	 * Calcola l'associazione id-nomeFamiglia usando come parametri la series e il locale
	 * @param key nel formato "<nome>,<series>,<locale>", ad esempio "familyIdAndNames,0,it_IT"
	 * @return List di array[2] con {id, nome}
	 */
	// TODO questa forse non serve più a meno che non si usi per la lista testuale famiglie
	private Object loadFamilyAndNames(String key) {
		String[] parts = key.split(KEY_SEPARATOR_1_STR);
		List<String[]> familyIdAndNames = new ArrayList<>();
		// String objectName = parts[0];
		String series = StringUtils.trimToNull(parts[1]);
		String localeName = parts[2];
		Locale locale = LocaleUtils.toLocale(localeName);
		// Aggiungo primo elemento tendina (quando niente è selezionato)
		familyIdAndNames.add(new String[] {"-1", messageSource.getMessage("local.page.prodotti.filter.famiglia", null, locale)});
		// Lista di array [long, FamigliaNome]
		List<Object[]> familyIdAndNamesFromDb = famigliaDao.findAllFamilyNames(series, locale);
		for (Object[] familyIdAndName : familyIdAndNamesFromDb) {
			String id = Long.toString((Long)familyIdAndName[0]);
			// TODO why go through FamigliaNome? Just get the string directly in the query !!!
			String name = ((FamigliaNome)familyIdAndName[1]).getValue();
			familyIdAndNames.add(new String[] {id, name});
		}
		return familyIdAndNames;
	}

	///////////////////////////////////////////////////////////
	// Init

	@PostConstruct
	protected void init() {
		int refreshMinutes = 2;
		// In sviluppo il timeout è 2 secondi, in produzione 2 minuti
		TimeUnit timeUnit = config.isDevelopmentEnvironment()?TimeUnit.SECONDS:TimeUnit.MINUTES;
		cache = CacheBuilder.newBuilder()
	       .maximumSize(CACHESIZE)
	       .refreshAfterWrite(refreshMinutes, timeUnit) // Rinfresca dopo due minuti dall'inserimento dell'oggetto
	       .build(
	           new CacheLoader<String, Object>() {
	             public Object load(String key) {
	               return loadFromDatabase(key);
	             }

	             public ListenableFuture<Object> reload(final String key, Object prevValue) {
	                   // asynchronous!
	                   ListenableFutureTask<Object> task = ListenableFutureTask.create(new Callable<Object>() {
	                     public Object call() {
	                       return loadFromDatabase(key);
	                     }
	                   });
	                   taskExecutor.execute(task);
	                   return task;
	               }

	           }
	       );
	}

}
