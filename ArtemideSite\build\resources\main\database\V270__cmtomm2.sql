# Convert cm to mm in configurator shapes

UPDATE ConfiguratorShape SET
    diameter = IF(diameter = '', '', CAST(CAST(diameter AS DECIMAL(20, 1)) * 10 AS UNSIGNED)),
    radius = IF(radius = '', '', CAST(CAST(radius AS DECIMAL(20, 1)) * 10 AS UNSIGNED)),
    width = IF(width = '', '', CAST(CAST(width AS DECIMAL(20, 1)) * 10 AS UNSIGNED)),
    length = IF(length = '', '', CAST(CAST(length AS DECIMAL(20, 1)) * 10 AS UNSIGNED))
    where length is null or length not like "%#%";

# Fix special case length: 30.1#31.5, 27.6#33, 200#450

UPDATE ConfiguratorShape SET
    length = IF(length = '', '', 
                CONCAT(
                    CAST(CAST(SUBSTRING_INDEX(length, '#', 1) AS DECIMAL(20, 1)) * 10 AS UNSIGNED), 
                    '#', 
                    CAST(CAST(SUBSTRING_INDEX(length, '#', -1) AS DECIMAL(20, 1)) * 10 AS UNSIGNED)
                )
    ),
    diameter = IF(diameter = '', '', CAST(CAST(diameter AS DECIMAL(20, 1)) * 10 AS UNSIGNED)),
    radius = IF(radius = '', '', CAST(CAST(radius AS DECIMAL(20, 1)) * 10 AS UNSIGNED)),
    width = IF(width = '', '', CAST(CAST(width AS DECIMAL(20, 1)) * 10 AS UNSIGNED))
    where length like "%#%";

    
# This was missing from previous query

UPDATE Prodotto SET
    length = IF(length = '', '', CAST(CAST(length AS DECIMAL(20, 1)) * 10 AS UNSIGNED)),
    depth = IF(depth = '', '', CAST(CAST(depth AS DECIMAL(20, 1)) * 10 AS UNSIGNED)),
    width = IF(width = '', '', CAST(CAST(width AS DECIMAL(20, 1)) * 10 AS UNSIGNED)),
    radius = IF(radius = '', '', CAST(CAST(radius AS DECIMAL(20, 1)) * 10 AS UNSIGNED)),
    diameter = IF(diameter = '', '', CAST(CAST(diameter AS DECIMAL(20, 1)) * 10 AS UNSIGNED)),
    cutoutLength = IF(cutoutLength = '', '', CAST(CAST(cutoutLength AS DECIMAL(20, 1)) * 10 AS UNSIGNED)),
    cutoutWidth = IF(cutoutWidth = '', '', CAST(CAST(cutoutWidth AS DECIMAL(20, 1)) * 10 AS UNSIGNED)),
    cutoutDiameter = IF(cutoutDiameter = '', '', CAST(CAST(cutoutDiameter AS DECIMAL(20, 1)) * 10 AS UNSIGNED)),
    baseDiameter = IF(baseDiameter = '', '', CAST(CAST(baseDiameter AS DECIMAL(20, 1)) * 10 AS UNSIGNED)),
    baseWidth = IF(baseWidth = '', '', CAST(CAST(baseWidth AS DECIMAL(20, 1)) * 10 AS UNSIGNED)),
    baseLength = IF(baseLength = '', '', CAST(CAST(baseLength AS DECIMAL(20, 1)) * 10 AS UNSIGNED)),
    recessedDepth = IF(recessedDepth = '', '', CAST(CAST(recessedDepth AS DECIMAL(20, 1)) * 10 AS UNSIGNED)),
    maxExtensionLength = IF(maxExtensionLength = '', '', CAST(CAST(maxExtensionLength AS DECIMAL(20, 1)) * 10 AS UNSIGNED)),
    maxHeightFromCeiling = IF(maxHeightFromCeiling = '', '', CAST(CAST(maxHeightFromCeiling AS DECIMAL(20, 1)) * 10 AS UNSIGNED)),
    maxExtensionHeight = IF(maxExtensionHeight = '', '', CAST(CAST(maxExtensionHeight AS DECIMAL(20, 1)) * 10 AS UNSIGNED))
    where height = "640/940";
    
    
