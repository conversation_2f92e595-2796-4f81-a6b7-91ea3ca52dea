<!DOCTYPE html>
<html xmlns:yada="http://www.yadaframework.net" xmlns:th="http://www.thymeleaf.org">
<head>
	<meta charset="UTF-8">
</head>

<!--/*
Template for <yada:datatable> resulting HTML.
It is used internally by the yada dialect processor to render the <yada:datatable> tag but could be included directly in a HTML template if needed.
*/-->

<body th:remove="tag">

<div class="yadaDataTableBlock" th:with="structure=${yadaDataTable.structure}">

	<table th:id="${yadaDataTable.id}" class="table" th:classappend="${structure.cssClasses}?:'table-striped no-wrap'">
		<thead>
			<tr>
				<th th:if="${structure.selectCheckbox}" th:with="title=${structure.selectCheckboxTitle}">
					<input type="checkbox" class="yada_selectAllNone" th:title="${title!=''}?#{${title}}">
				</th>
				<th th:each="column : ${structure.columns}" th:text="#{${column.headerText}}">Name</th>
				<th th:if="${!#strings.isEmpty(structure.commandsTitle)}" th:with="title=${structure.commandsTitle}" th:text="${title}?#{${title}}">Commands</th>
			</tr>
		</thead>
		<tfoot th:if="${structure.showFooter}">
			<tr>
				<th th:if="${structure.selectCheckbox}"></th> <!-- /* Empty for select column */-->
				<th th:each="column : ${structure.columns}" th:text="#{${column.headerText}}">Name</th>
				<th th:if="${structure.commandsTitle}"></th> <!-- /* Empty for command column */-->
			</tr>
		</tfoot>
	</table>

	<!--/* Toolbar */-->
	<div class="yadaDataTableToolbar">
		<th:block th:each="button : ${structure.buttons}" th:with="hasRole = ${button.roles==null || @userSession.currentUserProfile.hasAnyRoleId(button.roles)}">
			<a class="btn"
				th:if="${button.showCommandIcon==null && hasRole && (structure.selectCheckbox || button.global)}"
				href="javascript:void(0)"
				th:data-idname="${button.idName}"
				th:data-buttontype="${button.type}"
				th:disabled="${!button.global}"
				th:classappend="|${button.toolbarCssClass?:''} ${button.global?'yada_global':'disabled'} ${button.multiRow?'yada_multirow':''}|">
				<th:block th:if="${button.icon!=null}" th:utext="${button.icon}"><i class="bi bi-pencil"></i></th:block>
				<span th:text="#{${button.text}}">Add</span>
			</a>
		</th:block>
	</div>
	
	<!--/* Initialization code */-->
	<script th:inline="javascript">
	 	/*<![CDATA[*/	
		yada.ready(function(){
			const dataTableJson = /*[[${yadaDataTable}]]*/ "dummy";
			// Using the preprocessor so that any thymeleaf instructions are processed correctly
			const ajaxUrl = /*[[__${yadaDataTable.ajaxUrl}__]]*/ "dummy";
			const languageUrl = /*[[__${yadaDataTable.languageUrl}__]]*/ "dummy";
			// Fix button urls and localized confirm messages
			let buttonType;
			/*[# th:each="button : ${yadaDataTable.structure.buttons}"]*/
				buttonType = /*[[${button.type}]]*/ "dummy"; 					
    			dataTableJson.structure.buttons.filter(b => b.type==buttonType).forEach(function(b) {
   					b.url = /*[[__${button.url}__]]*/ "dummy";
   					/*[# th:if="${button.confirmDialog!=null}"]*/
   					b.confirmDialog.confirmTitle = /*[[#{${button.confirmDialog.confirmTitle}}]]*/ "dummy";
   					b.confirmDialog.confirmOneMessage = /*[[#{${button.confirmDialog.confirmOneMessage}}]]*/ "dummy";
   					b.confirmDialog.confirmManyMessage = /*[[#{${button.confirmDialog.confirmManyMessage}}]]*/ "dummy";
   					b.confirmDialog.confirmButtonText = /*[[#{${button.confirmDialog.confirmButtonText}}]]*/ "dummy";
   					b.confirmDialog.abortButtonText = /*[[#{${button.confirmDialog.abortButtonText}}]]*/ "dummy";
		   			/*[/]*/
    			}); 
   			/*[/]*/
			
			const commandColumnName = /*[[${T(net.yadaframework.persistence.YadaDataTableDao).COLUMN_COMMAND}]]*/ "dummy";
			const preprocessorName = /*[[${preprocessor}]]*/ "dummy"; // Becomes 'null' when missing
			const postprocessorName = /*[[${postprocessor}]]*/ "dummy"; // Becomes 'null' when missing
			
			const currentUserRoles = /*[[${@userSession.LoggedInUserRoles}]]*/ "dummy";
			
			yada.dataTable(dataTableJson, ajaxUrl, languageUrl, commandColumnName, preprocessorName, postprocessorName, currentUserRoles);
		});
		/*]]>*/
	</script>

</div>
	
</body>