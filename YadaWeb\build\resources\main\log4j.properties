# This file resolves the tomcat message "No appenders could be found for logger (org.jboss.logging)"
log4j.rootLogger=INFO, console
#log4j.org.jboss.logging=INFO, console

# System.out.println appender for all classes
log4j.appender.console=org.apache.log4j.ConsoleAppender
log4j.appender.console.threshold=INFO
log4j.appender.console.layout=org.apache.log4j.PatternLayout
log4j.appender.console.layout.ConversionPattern=(log4j) %-5p %c: %m%n

