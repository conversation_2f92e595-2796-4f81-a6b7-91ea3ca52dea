# Modifica per titolo del project

DROP TABLE Project_title;
create table Project_titlePartOne (Project_id bigint not null, titlePart<PERSON><PERSON> varchar(255), locale varchar(32) not null, primary key (Project_id, locale)) engine=InnoDB;
create table Project_titlePartTwo (Project_id bigint not null, titlePartTwo varchar(255), locale varchar(32) not null, primary key (Project_id, locale)) engine=InnoDB;
alter table Project_titlePartOne add constraint FKisymty0b5lkx90og3a296ed7e foreign key (Project_id) references Project (id);
alter table Project_titlePartTwo add constraint FK9fpwuoa25c6psniy3jyow9nvx foreign key (Project_id) references Project (id);

