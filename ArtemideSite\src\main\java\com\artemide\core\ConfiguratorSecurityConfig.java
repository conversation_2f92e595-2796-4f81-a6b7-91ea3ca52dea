package com.artemide.core;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.annotation.Order;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.config.http.SessionCreationPolicy;
import org.springframework.security.web.SecurityFilterChain;
import org.springframework.security.web.savedrequest.HttpSessionRequestCache;

import jakarta.servlet.DispatcherType;
import net.yadaframework.core.YadaConfiguration;
import net.yadaframework.security.YadaLocalePathRequestCache;
import net.yadaframework.security.YadaSecurityConfig;

/**
 * Security configuration per la sezione "configurator".
 */
@Configuration
@Order(9)
@EnableWebSecurity
public class ConfiguratorSecurityConfig extends YadaSecurityConfig {
	@Autowired private YadaConfiguration yadaConfiguration;

	public ConfiguratorSecurityConfig() {
		loginUrl = "/configurator/login";
		loginUrlAjax = "/configurator/login";
		loginPost = "/configurator/loginPost";
	}

	@Bean
	public SecurityFilterChain configuratorSecurityFilterChain(HttpSecurity http) throws Exception {
		// Can't use super.configure(http) because AntPathRequestMatcher("/configurator/**") would be ignored
		// because of the presence of another .authorizeHttpRequests()
		// so we just copy the configuration over from the superclass.
		// super.configure(http);
		successHandler.setDefaultTargetUrlNormalRequest("/configurator/choice");
		successHandler.setDefaultTargetUrlAjaxRequest("/yadaLoginSuccess?targetUrl=/configurator/choice");
		failureHandler.setFailureUrlAjaxRequest(loginUrlAjax);
		failureHandler.setFailureUrlNormalRequest(loginUrl);
		successHandler.setTargetUrlParameter("targetUrl"); // This has precedence when set in the login form
		super.logoutSuccessHandler.setDefaultTargetUrl("/configurator"); // language path will be added in the handler
//		super.failureHandler.setFailureUrlNormalRequest("/configurator/start");
//		super.failureHandler.setFailureUrlAjaxRequest("/configurator/login");
		
//		// The mapping matches URLs using the following rules:
//		// ? matches one character
//		// * matches zero or more characters
//		// ** matches zero or more 'directories' in a path
//		// Patterns which end with /** (and have no other wildcards) are optimized by
//		// using a substring match
		http
	        .headers(headers -> headers.disable())
	        .csrf(csrf -> csrf.disable())
	        .sessionManagement(sessions -> sessions.sessionCreationPolicy(SessionCreationPolicy.IF_REQUIRED))
			.logout(logout -> {
				logout.logoutUrl("/configurator/logout")
					.logoutSuccessHandler(logoutSuccessHandler);
			})
			.formLogin(formLogin -> formLogin
				.loginPage(loginUrl)
				.loginProcessingUrl(loginPost)
				.successHandler(successHandler)
				.failureHandler(failureHandler))
	        .exceptionHandling(exceptionHandling -> {
	            // This is needed to redirect to a language-specific login url
	            exceptionHandling.authenticationEntryPoint(new CustomAuthenticationEntryPoint());
	        })
	        .requestCache(requestCache -> {
	            if (yadaConfiguration.isLocalePathVariableEnabled()) {
	                // Resetto la RequestCache in modo che salvi le request di qualunque tipo, anche ajax,
	                // altrimenti il meccanismo del redirect alla pagina di partenza non funziona con le chiamate ajax.
	                requestCache.requestCache(new YadaLocalePathRequestCache());
	            } else {
	                requestCache.requestCache(new HttpSessionRequestCache());
	            }
	        })
			.securityMatcher("/configurator/**")
				.authorizeHttpRequests(
					authorize -> 
						authorize
							.requestMatchers("/configurator/my/**").authenticated()
							.requestMatchers("/configurator/**").permitAll()
							.dispatcherTypeMatchers(DispatcherType.FORWARD).permitAll()
							);

		return http.build();
    }
}
