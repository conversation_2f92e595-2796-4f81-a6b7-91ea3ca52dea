<?xml version="1.0" encoding="UTF-8" ?>
<config>
	<info>
		<env>prod</env>
		<build>${build}</build>
		<date>@@RELEASEDATE_PLACEHOLDER@@</date> <!-- Injected by gradle -->
	</info>
	<paths>
		<basePath>/srv/amdprod</basePath>
		<!-- The name is the folder name inside basePath -->
		<!-- The url can either be an absolute url like "/contents", or a full url like "http://cdn.com/artemide/contents" -->
		<contentDir name="contents" url="/contents"> <!-- La url può essere assoluta per distribuire i contenuti -->
		</contentDir>
		<prodottoImageDir>/immagini/prodotto</prodottoImageDir>
		<prodottoFileDir>/documenti/prodotto</prodottoFileDir>
		<publicationImageDir>/immagini/publication</publicationImageDir>
		<publicationFileDir>/documenti/publication</publicationFileDir>
		<uploadsDir>uploads</uploadsDir>
		<!-- OLD: path per le pagine esterne -->
		<!-- OLD: Il folder "external" serve solo per sapere se una pagina localizzata esiste, perché se non esiste viene presa la versione inglese. -->
		<errorPageForward>/home</errorPageForward>
		<notificationModalView>/amdModalNotify</notificationModalView>
		<serverAddress>https://www.artemide.com</serverAddress>
		<instructionsBasepath>/home/<USER>/importExport/productInstructions</instructionsBasepath>
		<!-- path to import product files -->
		<basepathImportFiles>/home/<USER>/importExport/productFiles</basepathImportFiles>
		<configuratorRegressionTest path="${config/paths/basePath}/regression">
			<enabled>true</enabled>
			<defaultCapture>true</defaultCapture>
		</configuratorRegressionTest>
	</paths>
	<!-- LEGACY SECTION: for new values use paths instead -->
	<resources>
		<!-- URL per andare a prendere i docs -->
		<baseurldocs>/contents/documenti</baseurldocs> 
		<!-- Path dove il sito salva i pdf di scheda prodotto/progetto -->
		<basepathpdf>/srv/amdprod/contents/pdfcache</basepathpdf>
		<!-- Path dove il sito salva i pdf creati dagli utenti -->
		<basepathMyPdf>/srv/amdprod/contents/mypdf</basepathMyPdf>
		<!-- URL per andare a prendere i pdf creati dagli utenti -->
		<baseurlMyPdf>/contents/mypdf</baseurlMyPdf> 
		<!-- URL per andare a prendere i pdf -->
		<baseurlpdf>/contents/pdfcache</baseurlpdf> 
		<!-- path per i font -->
		<fontServerPath>/usr/share/fonts/truetype</fontServerPath>
		<!-- path per le etichette -->
		<energyLabelPath>/srv/amdprod/energylabels</energyLabelPath>
	</resources>	
	<!-- Sets the max file and request size, but not the max post size that should be set in server.xml -->
	<maxFileUploadSizeBytes>4000000000</maxFileUploadSizeBytes>
	<!-- Need to set this when using StreamingResponseBody -->
	<!-- 40 min to export the full architectural product catalog -->
	<asyncTimeoutMinutes>40</asyncTimeoutMinutes>
	
	<application name="sito" version="v4.1-prod">
		<typologyOrder>0,4,8,2,5,18,17,3,7,6,1,9,10,11,12,13,14,15,16</typologyOrder> <!-- Still used? -->
		<recentlyViewedFamilyMax>4</recentlyViewedFamilyMax> <!-- Still used? -->
		<promotion>
			<blackweek url="/blackweek" method="promoBlackweek" addToMap="false">
				<!-- ISO format: yyyy-mm-dd -->
				<showFrom time="01:00">2024-11-03</showFrom>
				<showTo time="23:59">2024-12-03</showTo>
				<countries>
					<!-- The tag is the ISO3 for the country found in the database -->
					<!-- The lang attribute is the ISO2 for the language used in the language menu
						 It is used to preselect the country given the current language choice.
						 It can be omitted.
						 REMOVED in 2024 so that nothing is preselected.
					-->
					<!-- The subselect attribute is the name of the column in the Address table 
						 to use as a second select after choosing the country. Tested values are only "provincia" and "stato".
						 There must exist a key in message.properties for each value, like "blackfriday.filter.stato"
					-->
					<!-- The group attribute is a grouping of countries under the same pseudo-country that will appear
						 in the dropdown instead of the individual country name. All stores of the group will be
						 shown when the group is selected.
						 Currently subselect is not implemented for groups: an exception will be thrown. 
					-->
					<CZE>country.checkrepublic</CZE>
					<DEU subselect="provincia">country.germany</DEU>
					<ESP>country.spain</ESP>
					<ITA subselect="stato">country.italy</ITA>
					<AUT>country.austria</AUT>
					<POL>country.poland</POL>
					<PRT>country.portugal</PRT>
					<CHE>country.switzerland</CHE>
					<SVK>country.slovakia</SVK>
					<!-- Country names in a group are currently not used and are for documentation only -->
					<EST group="group.baltic">Eesti</EST>
					<LVA group="group.baltic">Latvija</LVA>
					<LTU group="group.baltic">Lietuva</LTU>
					<ALB group="group.balcan">Shqipëri</ALB>
					<BIH group="group.balcan">Bosna i Hercegovina</BIH>
					<BGR group="group.balcan">България</BGR>
					<HRV group="group.balcan">Hrvatska</HRV>
					<KOS group="group.balcan">Kosova</KOS> <!-- KOS is not the correct code but that's what is in the DB for Kosovo -->
					<MNE group="group.balcan">Crna Gora</MNE>
					<ROU group="group.balcan">România</ROU>
					<SRB group="group.balcan">Srbija</SRB>
					<SVN group="group.balcan">Slovenija</SVN>
					<UKR group="group.balcan">Україна</UKR>
				</countries>
			</blackweek>
			<netflix url="/eclissesquidgame" method="promoNetflix" addToMap="false">
				<!-- ISO format: yyyy-mm-dd -->
				<showFrom time="00:01">2025-06-17</showFrom>
				<showTo time="23:59">2026-06-17</showTo>
				<countries world="true"> <!-- world="true" means that all countries are used even when not individually configured -->
					<DEU subselect="provincia"></DEU>
					<ITA subselect="stato"></ITA>
					<!-- Country names in a group are currently not used and are for documentation only -->
					<EST group="group.baltic">Eesti</EST>
					<LVA group="group.baltic">Latvija</LVA>
					<LTU group="group.baltic">Lietuva</LTU>
					<ALB group="group.balcan">Shqipëri</ALB>
					<BIH group="group.balcan">Bosna i Hercegovina</BIH>
					<BGR group="group.balcan">България</BGR>
					<HRV group="group.balcan">Hrvatska</HRV>
					<KOS group="group.balcan">Kosova</KOS> <!-- KOS is not the correct code but that's what is in the DB for Kosovo -->
					<MNE group="group.balcan">Crna Gora</MNE>
					<ROU group="group.balcan">România</ROU>
					<SRB group="group.balcan">Srbija</SRB>
					<SVN group="group.balcan">Slovenija</SVN>
					<UKR group="group.balcan">Україна</UKR>
				</countries>
			</netflix>
			<ilio url="/ilio10specialedition" method="promoIlio" addToMap="true">
				<!-- ISO format: yyyy-mm-dd -->
				<showFrom time="01:00">2024-03-18</showFrom>
				<showTo time="23:59">2025-03-17</showTo>
				<countries>
					<!-- The tag is the ISO3 for the country found in the database -->
					<!-- The lang attribute is the ISO2 for the language used in the language menu
						 It is used to preselect the country given the current language choice.
						 It can be omitted.
					-->
					<!-- The subselect attribute is the name of the column in the Address table 
						 to use as a second select after choosing the country. Tested values are only "provincia" and "stato".
					-->
					<DEU lang="de">country.germany</DEU>
					<AUT>country.austria</AUT>
					<CHE>country.switzerland</CHE>
				</countries>
			</ilio>
		</promotion>
	</application>

	<bootstrapVersion>4</bootstrapVersion>
	
	<selenium>
		<percy>false</percy> <!-- true to use Percy, false to save screenshots -->
	</selenium>
	
	<configurator version="v0.7" beta="true">
		<aolv1>false</aolv1>
		<a24v1>false</a24v1>
		<floorPlanDir>floorPlan</floorPlanDir> <!-- Where floor plans are stored, relative to the contents/configurator folder -->
	</configurator>
	
	<cache>
		<!-- When the cache is disabled, it could still be enabled via dashboard -->
		<subfamily enabled="true">
			<!-- Numero massimo di prodotti (righe di subfamily) messi in cache.
				Un valore troppo basso vanifica la cache.
				Un valore troppo alto causa OutOfMemory.
				In tutto il DB ci sono 15k prodotti in 900 sottofamiglie per lingua ma la metà non sono pubblicati.
				Le sottofamiglie con più di 50 prodotti sono 60 e raccolgono 8k prodotti.
				ATTENZIONE: il criterio con cui eliminare una sottofamiglia dalla cache è indipendente dalla
				sua grandezza ma considera solo l'utilizzo.
				Una sottofamiglia viene eliminata dalla cache quando la cache è piena, solo se è stata usata 
				meno spesso o meno di recente rispetto ad altre (credo anche se è stale).
				Una sottofamiglia viene ricaricata al successivo utilizzo quando diventa stale, ma il chiamante
				riceve ancora quella vecchia perché il reload è asincrono.
				Quindi non è un problema avere uno stale basso perché gli utenti comunque non sperimentano il tempo di
				reload se il valore esiste già in cache anche se stale.
			-->
			<!-- 47653 elementi occupano circa 744MB -->
			<maxweight>99000</maxweight>
			<!-- Questo può essere relativamente alto visto che ogni edit di subfamily in genere pulisce l'elemento dalla cache -->
			<!-- Dopo 24 ore un'entry diventa stale e ricaricata alla successiva richiesta -->
			<staleAfterSeconds>86400</staleAfterSeconds>
		</subfamily>
	</cache>
	
	<rest>
		<pivotal>
			<!-- Per disabilitare la connessione cancellare l'utente -->
			<url>https://crm.artemide.com/PVTApi/lead/</url>
			<user>artemide</user>
			<password>CdBT4AgIB0mMbUA</password>
		</pivotal>
	</rest>
	
	<newsletter> <!-- Questi valori sono stabiliti da contactlab -->
		<url>http://clientsection.contactlab.it/service/subscribe.php</url>
		<g>2000840</g>
		<wfc>
			<en>1520008408756</en>
			<it>1420008401113</it>
			<es>1620008405341</es>
			<fr>1720008404873</fr>
			<de>1820008402906</de>
			<ru>1520008408756</ru>
		</wfc>
	</newsletter>
	
	<i18n localePathVariable="true">
		<messageSource>
			<basename>messages</basename>
			<basename>countries</basename>
		</messageSource>
		<locale country="IT">it</locale>
		<locale country="GB" default="true">en</locale>
		<locale country="DE">de</locale>
		<locale country="ES">es</locale>
		<locale country="FR">fr</locale>
		<!-- <locale country="RU">ru</locale> --> <!-- Russo tolto per il momento -->
	</i18n>
	
	<database>
		<flywayTableName>schema_version</flywayTableName>
		<jndiname>java:comp/env/jdbc/amdprod</jndiname>
		<showSql>false</showSql>
		<databaseMigrationAtStartup outOfOrder="true">true</databaseMigrationAtStartup>
		<entityPackage>com.artemide.persistence.entity</entityPackage>
		<entityPackage>com.artemide.common.persistence.entity</entityPackage>
		<entityPackage>com.yr.babka37.entity</entityPackage>
		<entityPackage>com.yr.babka37.entity.localstring</entityPackage>
		<entityPackage>com.yr.entity</entityPackage>
		<entityPackage>com.yr.entity.localstring</entityPackage>
	</database>

	<lampCategories>
		<lampCategory>FLUO</lampCategory>
		<lampCategory>HALO</lampCategory>
		<lampCategory>HQI</lampCategory>
		<!-- nessun prodotto la usa <lampCategory>HIR</lampCategory>  -->
	</lampCategories>

	 <dimension targetImageExtension="jpg" preserveImageExtensions="gif">
		<!-- 
			# Configurazione dimensioni massime immagini
			# Formato: WIDTH,HEIGHT
	 	-->
		<designer>
			<!-- Queste sono le vecchie
			<small>230,170</small>
			<thumbnail>171,171</thumbnail>
			<image>340,250</image>  -->
			<!-- Queste sono le nuove
			La thumbnail viene usata solo nella pagina prodotti, di fianco al progetto
			La versione small non serve, usiamo questa anche su mobile/tablet
			 -->
			<thumbnail>960,457</thumbnail>
			<image>960,960</image>
		</designer>
		<catalogHero>
			<desktop>1920,1080</desktop>
			<mobile>768,432</mobile>
		</catalogHero>
		<familyLegacy>480,480</familyLegacy> <!-- Usata solo da ManagerController.fixFamiglia() -->
		<family>
			<small>480,480</small>
			<big>960,960</big>
			<large>960,480</large>
			<tall>480,960</tall>
		</family>
		<subfamily>
			<thumbnail>480,480</thumbnail> <!-- mostrata nel modal di scelta sottofamiglia -->
			<gallery>1920,1080</gallery>
			<galleryrecent>768,432</galleryrecent> <!-- recently viewed, ora usato per mobile hero -->
			<!-- Non usato in EVO 
			<gallerydescription>960,480</gallerydescription>
			-->
			<lightemission>30,30</lightemission>
			<configicon>60,60</configicon>
			<tunablewhite>600,190</tunablewhite> <!-- si usa solo l'altezza lasciando libera la larghezza -->
			<curvaFotometrica>400,190</curvaFotometrica> <!-- si usa solo l'altezza lasciando libera la larghezza -->
		</subfamily>
		<articolo>85,85</articolo>
		<lamp>30,30</lamp>
		<led>30,30</led>
		<homepagegallery>1920,1080</homepagegallery>
		<homepagegallerymobile>768,738</homepagegallerymobile>
		<homepageimage>1920,1010</homepageimage>
		<homepageimagemobile>768,427</homepageimagemobile>
		<homepagebanner>1920,409</homepagebanner>
		<homepagebannermobile>768,384</homepagebannermobile>
		<!-- The thumbnail image can be any proportions, we just have to constrain the size inside the box -->
		
		<!-- <eventonews>450,540</eventonews> 		
		<eventonewsgallery>1920,1080</eventonewsgallery>
		<eventonewsgallerymobile>768,432</eventonewsgallerymobile> -->
		<gallerymobilemicro>375,500</gallerymobilemicro>
		<gallerymobile>768,1024</gallerymobile>
		<product>
			<gallery>960,960</gallery>
			<!-- La thumbnail viene adesso solo usata nella tabella prodotti -->
			<thumbnail>240,240</thumbnail>
			<ldtimageSmall>230,230</ldtimageSmall>
			<ldtimage>800,600</ldtimage>
			<silhouette>800,600</silhouette>
			<colorImage>48,48</colorImage>
			<beam>18,18</beam>
		</product>
		<news>
			<topindice>
				<desktop>1920,1400</desktop>
				<mobile>768,610</mobile>
			</topindice>
			<thumbnail>
				<desktop>1920,1374</desktop>
				<mobile>768,533</mobile>
			</thumbnail>
		</news>
		<project>
			<bigimage>
				<desktop>1920,975</desktop>
				<mobile>768,478</mobile>
			</bigimage>
			<smallimage>
				<desktop>960,960</desktop>
				<mobile>768,478</mobile>
			</smallimage>
		</project>
		<!-- pagemodule serve per news, projects e home (Page Builder) -->
		<pagemodule>
			<!-- La risoluzione di un A4 a 300dpi è 2480x3508  -->
			<type1>
				<desktop>1920,973</desktop>
				<mobile>768,744</mobile>
				<pdf>2271,1000</pdf>
			</type1>
			<!-- type2 solo testo  -->
			<type3>
				<desktop>1920,1106</desktop>
				<mobile>768,483</mobile>
				<pdf>1521,958</pdf>
			</type3>
			<type4>
				<desktop>640,720</desktop>
				<mobile>768,768</mobile>
				<pdf>837,946</pdf>
			</type4>
			<type5> <!-- uguale al 4 -->
				<desktop>640,720</desktop>
				<mobile>768,768</mobile>
				<pdf>837,946</pdf>
			</type5>
			<type6> <!-- Cover image -->
				<desktop>1920,1080</desktop>
				<mobile>768,432</mobile> <!-- Deve avere la stessa proporzione 16/9 del desktop -->
			</type6>
			<type7>
				<desktop>1220,720</desktop>
				<mobile>648,384</mobile>
				<pdf>1779,996</pdf>
			</type7>
			<!-- type8 solo testo  -->
			<type9> <!-- carrousel del modulo -->
				<desktop>1920,1200</desktop>
				<mobile>768,427</mobile>
				<!-- La versione pdf non serve perché si usa la prima desktop -->
			</type9>
			<!-- type10 carrousel dei prodotti  -->
			<type11>
				<desktop>1920,1032</desktop>
				<mobile>768,434</mobile>
				<pdf>2271,996</pdf>
			</type11>
			<type12>
				<desktop>640,457</desktop>
				<mobile>768,480</mobile>
				<pdf>1079,787</pdf>
				<image1>
					<desktop>640,640</desktop>
					<mobile>768,768</mobile>
					<pdf>787,787</pdf>
				</image1>
			</type12>
			<!-- type13 bottone link -->
			<!-- type14 solo testo -->
			<type15>
				<desktop>845,1200</desktop>
				<mobile>768,1082</mobile>
				<!-- pdf 837,1570  -->
				<image1>
					<desktop>845,1200</desktop>
					<mobile>768,1082</mobile>
					<!-- pdf 837,1570  -->
				</image1>
			</type15>
			<type16>
				<desktop>1920,1200</desktop>
				<mobile>768,427</mobile>
				<!-- La versione pdf non serve perché si usa la prima desktop -->
				<image1>
					<desktop>1920,1200</desktop>
					<mobile>768,427</mobile>
					<!-- La versione pdf non serve perché si usa la prima desktop -->
				</image1>
			</type16>
			<!-- HOME -->
			<!-- Per ora non uso dimensioni diverse tra desktop e mobile, ma la più grande tra i due
				 nel senso che un'immagine desktop a mezzo schermo è più piccola di una mobile a pieno schermo.
				 Assumo il valore di 1000 pixel per le immagini mobile. -->
			<type100> <!-- Home Carousel con cover image sempre la stessa  -->
				<desktop>1920,1080</desktop>
			</type100>
			<type101> <!-- Collection/Product con immagine landscape o quadrata -->
				<desktop>1200,675</desktop> <!-- Landscape -->
				<image1><desktop>1000,1000</desktop></image1> <!-- Square -->
			</type101>
			<type102> <!-- Catalogs con immagini quadrate  -->
				<desktop>1000,1000</desktop>
			</type102>
			<type103>	<!-- quella sopra -->
				<desktop>950,760</desktop>
				<mobile>686,380</mobile>	<!-- usata per tablet -->
				<pdf>355,197</pdf> <!-- usata per mobile -->
				<image1>	<!-- quella sotto -->
					<desktop>950,328</desktop>
					<mobile>333,333</mobile>
					<pdf>168,168</pdf>
				</image1>
			</type103>
			<type105> <!-- Journal con immagini quadrate  -->
				<desktop>1000,1000</desktop>
			</type105>
			<type107> <!-- Journal con immagini quadrate  -->
				<desktop>946,946</desktop>
			</type107>
			<type108> <!-- Scelta immagine: horizontal, vertical or square -->
				<desktop>450,300</desktop> <!-- horizontal -->
				<image1><desktop>300,450</desktop></image1> <!-- vertical -->
				<image2><desktop>350,350</desktop></image2> <!-- square -->
			</type108>
			<!-- TODO altre dimensioni per moduli scenario etc. -->
		</pagemodule>
		
		<publications>
			<type>
				<t0>424,575</t0> <!-- Catalogues -->
				<t1>411,577</t1> <!-- Brochures -->
				<t2>406,572</t2> <!-- Lighting Fields -->
				<t3>376,576</t3> <!-- Magazines -->
				<t4>420,575</t4> <!-- Corporate -->
				<t5>411,577</t5> <!-- Certifications -->
				<t6>411,577</t6> <!-- Leaflets -->
			</type>
		</publications>
	</dimension>
	
	<pageBuilder>
		<gallery layoutType="hero">
			<attribute name="text5local" required="true" editor="true">Title (multiline) (HTML)</attribute>
			<attribute name="text1">Designer (HTML)</attribute>
			<attribute name="text2">Photographer (HTML)</attribute>
			<attribute name="text8local" editor="false">Button text (HTML)</attribute>
			<attribute name="data2">Button URL (http...)</attribute>
			<attribute name="flag1">White button (black when unset)</attribute>
			<attribute name="data3">Mobile video offset (pixels without px)</attribute>
			<attribute name="data4">Tablet video offset (not implemented yet)</attribute>
			<attribute name="multipartImage" required="false">Video cover</attribute>
			<attribute name="multipartVideo" required="false">Video</attribute>
			<attribute name="data1">Text color (CSS)</attribute>
		</gallery>
		<gallery layoutType="imageLinkAndTest">
			<attribute name="text5local" required="true" editor="true">Description (multiline) (HTML)</attribute>
			<attribute name="text8local" editor="false">Button text (HTML)</attribute>
			<attribute name="data2">Button URL (http...)</attribute>
			<attribute name="multipartImage" required="false">Upload Image</attribute>
		</gallery>
		<gallery layoutType="imageVideoAndCropFlag"> <!-- Used for collection/product/journal -->
			<attribute name="text5local" required="false" editor="true">Image text (HTML)</attribute>
			<attribute name="text6local" required="false" editor="false">Image subtext (year) (HTML)</attribute>
			<!-- Image can't be required else other fields can't be changed on edit -->
			<attribute name="multipartImage" required="false">Slide image / Video cover</attribute>
			<attribute name="multipartVideo" required="false">Video 1920x1080</attribute>
			<attribute name="data1">Slide text color (CSS)</attribute>
			<attribute name="data2">Navigation color (CSS)</attribute>
		</gallery>
		<gallery layoutType="imageVideoProjectDesigner"> <!-- Used for collection/product/journal -->
			<attribute name="text5local" required="false" editor="true">Project Name (HTML)</attribute>
			<attribute name="data5">Project Link</attribute>
			<attribute name="text1">Designer (HTML)</attribute>
			<attribute name="data1">Designer Link</attribute>
			<!-- Image can't be required else other fields can't be changed on edit -->
			<attribute name="data2" required="true">Choose a format (horizontal, vertical or square)</attribute>
			<attribute name="multipartImage" required="false">Slide image / Video cover</attribute>
			<attribute name="multipartVideo" required="false">Upload Video</attribute>
		</gallery>
		<gallery layoutType="imageAndVideo"> <!-- Used for journal NOT USED ANYMORE -->
			<attribute name="multipartImage" required="false">Slide image / Video cover</attribute>
			<attribute name="multipartVideo" required="false">Video</attribute>
		</gallery>
		<gallery layoutType="justImages">
			<!-- Image can't be required else the "visible" flag can't be changed -->
			<attribute name="multipartImage" required="false">Slide image</attribute>	
		</gallery>
	</pageBuilder>
	
	<section>
		<products>
			<!-- Cambiare questo valore per cambiare la tendina di filtro sui cataloghi: design, architectural, etc. -->
			<!-- scenarios 9 è stato tolto nel 2020 -->
			<!-- Per i valori vedere /ArtemideCommon/src/main/resources/conf.coreoptions.xml -->
			<!-- 
				tolto il 4 per Novelties	
				<catalogFilterSeries>0, 1, 7, 6</catalogFilterSeries>	
			-->
				<!-- IMPORTANTE: tenere sincronizzato con FamilyController.catalogUrls -->
				<catalogFilterSeries>4, 0, 1, 7, 6</catalogFilterSeries>		
		</products>
	</section>
	
	<format>
		<!-- 
			# Formatters
			# Attenzione: tenerli sincronizzati con la classe ConverterFromString
		 -->
		 <datetime>
		 	<generic>yyyy-MM-dd HH:mm:ss</generic>
		 	<noSeconds>yyyy-MM-dd HH:mm</noSeconds>
		 </datetime>
		 <date>
		 	<generic>yyyy-MM-dd</generic>
		 	<slashed>dd/mm/yyyy</slashed>
		 	<datepicker>
		 		<!-- 
					# JQuery Date format, for datepicker (http://docs.jquery.com/UI/Datepicker/formatDate)
					# Deve corrispondere alla versione Java altrimenti la conversione non funziona
					# yy-mm-dd = 2011-04-10
					OCCHIO CHE SI CHIAMAVA format.date.generic.datepicker
		 		 -->
		 		yy-mm-dd
		 	</datepicker>
		 </date>
	</format>
	
	<email throwExceptions="true">
		<enabled>true</enabled>
		<from>
			<address><EMAIL></address>
			<name>Artemide</name>
		</from>
		<publications>
			<notifyEdits>
				<enabled>true</enabled>
				<!-- Can have multiple target tags -->
				<target><EMAIL></target>
			</notifyEdits>
		</publications>
		<ideas> 
			<!-- Proposte di designers - your ideas -->
			<to><EMAIL></to>
			<!-- Se ne possono aggiungere altre <to><EMAIL></to>  -->
		</ideas>
		<contactUs>
			<!-- Email di default quando nel database non esiste una mail per il country selezionato dall'utente.
			Usato anche per il configuratore -->
			<default><EMAIL></default>
			<!-- Per le richieste "Replacement request" e "Technical assistance request" -->
			<italianRequest><EMAIL></italianRequest>
			<globalRequest><EMAIL></globalRequest>
			<!-- Email usata per le richieste di progetto illuminotecnico -->
			<lightingProject><EMAIL></lightingProject>
		</contactUs>
		<logoImage>/template/email/Logo_Artemide_Rosso_400px.png</logoImage>
		<smtpserver>
			<host>email-smtp.eu-west-1.amazonaws.com</host>
			<port>587</port>
			<protocol>smtp</protocol>
			<username>${smtpserver_username}</username> <!-- amdprod-smtp-21 -->
			<password>${smtpserver_password}</password>
			<properties>mail.smtp.sendpartial=true</properties> <!--  If set to true, and a message has some valid and some invalid addresses, send the message anyway, reporting the partial failure with a SendFailedException. If set to false (the default), the message is not sent to any of the recipients if there is an invalid recipient address.  -->
			<properties>mail.smtp.auth=true</properties>
			<properties>mail.smtp.starttls.enable=true</properties>
			<properties>mail.smtp.quitwait=false</properties>
		</smtpserver>
	</email>

	<setup>
		<users>
			<user>
				<email><EMAIL></email>
				<password>klsdfn48ysse4ln8</password>
				<locale>it_IT</locale>
				<timezone>Europe/Rome</timezone>
				<role>USER</role>
				<role>MANAGER</role>
				<role>DEVELOPER</role>
				<role>ADMIN</role>
				<role>SALESFORCE</role>
			</user>
		</users>
	</setup>
	
	<security>
		<home>
			<previewExpirationDays>10</previewExpirationDays>
		</home>
		<!-- Questo valore viene usato lato javascript per controllare se la sessione è scaduta -->
		<sessionTimeoutMinutes>240</sessionTimeoutMinutes><!-- ATTENZIONE: questo va tenuto allineato con web.xml altrimenti la sessione potrebbe non scadere mai -->
		<passwordLength min='4' max='64' />
		<encodePassword>true</encodePassword>
		<maxFailedAttempts>20</maxFailedAttempts>
		<failedAttemptsLockoutMinutes>10</failedAttemptsLockoutMinutes>
		<autologinExpirationHours>48</autologinExpirationHours>
		<registration>
			<confirmationLink>/registrationConfirmation</confirmationLink>
		</registration>
		<roles>
			<!-- ATTENZIONE: l'ID e la KEY vanno stabiliti all'inizio e MAI PIU' CAMBIATI perchè vanno nel DB.
					Sono stati scelti valori non consecutivi per le ID in modo da poter eventualmente inserire valori in mezzo, 
					anche se ciò non ha effettivamente senso ma non si sa mai che si voglia un giorno ordinare in base all'ID. -->
			<role>
				<id>10</id>
				<key>SALESFORCE</key>
			</role>
			<role>
				<id>8</id>
				<key>ADMIN</key> <!-- La descrizione va in messages.properties -->
				<!-- Which roles can this role set/unset on any user: -->
				<handles>ADMIN</handles>
				<handles>DEVELOPER</handles>
				<handles>MANAGER</handles>
				<handles>SALESFORCE</handles>
				<handles>USER</handles>
			</role>
			<role>
				<id>6</id>
				<key>DEVELOPER</key>
			</role>
			<role>
				<id>4</id>
				<key>MANAGER</key>
				<handles>USER</handles>
				<handles>MANAGER</handles>
				<handles>SALESFORCE</handles>
			</role>
			<role>
				<id>2</id>
				<key>USER</key>
			</role>
		</roles>
		<keys>
			<recaptcha>
				<!-- These are to be set in the /srv/amdxxx/bin/security.properties file -->
				<siteKey>${recaptcha_sitekey}</siteKey>
				<secretKey>${recaptcha_secretkey}</secretKey>
			</recaptcha>
		</keys>
	</security>	
	
	<social>
		<!-- These are only for social login -->
		<facebook>
			<type>0</type>
			<appId>113150012436006</appId>
			<secret>ffc394c24ba6b08dd29e1f036baebbe5</secret>
		</facebook>
		<google> <!-- Attualmente questi sono quelli di collaudo -->
			<type>1</type>
			<clientId>638024765791-kupbgm50os644phgl0kjeq9bva1ng0s8.apps.googleusercontent.com</clientId>
			<secret>Hi0l3XmEsf6ev_Pwh5ipKy8m</secret>
		</google>
	</social>
	
	<storelocator> <!-- a cosa serve??????????? -->
		<mappa>
			<image>
				<baseurl>http://www.artemide.com/resources/images/icon/mappa/</baseurl>
				<ext>.png</ext>
			</image>
		</mappa>
	</storelocator>
	
	<dimmable> <!-- a cosa serve??????????? -->
		<baseurl>http://www.artemide.it/resources/images/dimmable/</baseurl>
		<ext>.png</ext>
	</dimmable>

	<!-- Lingue europee per le etichette energetiche - l'ordine NON viene mantenuto -->
	<iso2EuLanguages>
		<language>bg=български</language>
		<language>es=Español</language>
		<language>cs=Česká</language>
		<language>da=Dansk</language>
		<language>de=Deutsch</language>
		<language>et=Eesti</language>
		<language>el=ελληνικά</language>
		<language>en=English</language>
		<language>fr=Français</language>
		<language>hr=Hrvatski</language>
		<language>it=Italiano</language>
		<language>lv=Latvijas</language>
		<language>lt=Lietuvių</language>
		<language>hu=Magyar</language>
		<language>mt=Malti</language>
		<language>nl=Nederlandse</language>
		<language>pl=Polski</language>
		<language>pt=Português</language>
		<language>ro=Român</language>
		<language>sk=Slovenský</language>
		<language>sl=Slovenščina</language>
		<language>fi=Suomalainen</language>
		<language>sv=Svenska</language>
	</iso2EuLanguages>
		
	<shell>
		<risolutore>
			<executable>${config/paths/basePath}/bin/risolutore-funi/bin/runRisolutore.sh</executable>
		</risolutore>
		<zipWithRename> <!-- Where is this used? -->
			<executable>${config/paths/basePath}/bin/zipRename.sh</executable>
			<arg>${ZIPFILE}</arg>
			<arg>${ZIPNOTEFILE}</arg>
			<arg>${FILES}</arg>
		</zipWithRename>
		<headerLabelPdf> <!-- PDF scheda prodotto con almeno 2 pagine -->
			<executable>${config/paths/basePath}/bin/addPdfHeader.sh</executable>
			<arg>${TEXT}</arg>
			<arg>${FILENAME}</arg>
		</headerLabelPdf>
		<zipPdfProdotti>
			<executable>${config/paths/basePath}/bin/zipFolder.sh</executable>
			<arg>${FOLDERTOZIP}</arg>
			<arg>${ZIPFILENAME}</arg>
		</zipPdfProdotti>
		<deleteSchedaPdf> <!-- Cancella il pdf quando cambiano journal o project -->
			<executable>${config/paths/basePath}/bin/deleteSchedaPdf.sh</executable>
			<arg>${FILENAME}</arg>
		</deleteSchedaPdf>
		<pdf-chrome>
			<!-- Using nice so that the tomcat process is not impacted -->
			<executable>/usr/bin/nice</executable>
			<arg>-n</arg>
			<arg>19</arg>
			<arg>/usr/bin/node</arg>
			<arg>${BASEPATH}/bin/puppeteer/makepdf.js</arg>
			<arg>${URL}</arg>
			<arg>${URLPDFFOOTER}</arg>
			<arg>50</arg>
			<arg>50</arg>
			<arg>71</arg>
			<arg>50</arg>
			<arg>${FILENAMEOUT}</arg>
		</pdf-chrome>
		<convert>
			<executable>convert</executable>
			<arg>-auto-orient</arg>
			<arg>${FILENAMEIN}</arg>
			<arg>${FILENAMEOUT}</arg>
		</convert>
		<resize>
			<executable>convert</executable>
			<arg>-auto-orient</arg>
			<arg>${FILENAMEIN}</arg>
			<arg>-resize</arg>
			<arg><![CDATA[${W}x${H}]]></arg>
			<arg>${FILENAMEOUT}</arg>
		</resize>
		<dxf-export>
			<!-- Using nice so that the tomcat process is not impacted -->
			<executable>/usr/bin/nice</executable>
			<arg>-n</arg>
			<arg>19</arg>
			<arg>${config/paths/basePath}/bin/dxfExporter/DxfExporter</arg>
			<arg>${MODELROOTPATH}</arg>
			<arg>${FILENAMEIN}</arg>
			<arg>${FILENAMEOUT}</arg>
		</dxf-export>
		<blender-conversion>
			<!-- Using nice so that the tomcat process is not impacted -->
			<executable>/usr/bin/nice</executable>
			<arg>-n</arg>
			<arg>19</arg>
			<arg>${config/paths/basePath}/bin/blenderConvert.sh</arg>
			<arg>${FILENAMEIN}</arg>
			<arg>${FILENAMEOUT}</arg>
		</blender-conversion>
		<pdf-configurator timeoutseconds="400">
			<!-- Using nice so that the tomcat process is not impacted -->
			<executable>/usr/bin/nice</executable>
			<arg>-n</arg>
			<arg>19</arg>
			<arg>${config/paths/basePath}/bin/wkhtmltopdf</arg>
			<arg>-q</arg>
			<arg>--javascript-delay</arg>
			<arg>1000</arg>
			<arg>--footer-html</arg>
			<arg>${URLPDFFOOTER}</arg>
			<arg>${URL}</arg>
			<arg>${FILENAMEOUT}</arg>
		</pdf-configurator>
		<pdf-etichette>
			<executable>${config/paths/basePath}/bin/cpdf-etichette.sh</executable>
			<arg>${TEXT}</arg>
			<arg>${TEXTPOS}</arg>
			<arg>${FILENAMEIN}</arg>
			<arg>${FILENAMEOUT}</arg>
		</pdf-etichette>
		<pdf-scheda>
			<executable>${config/paths/basePath}/bin/wkhtmltopdf</executable>
			<arg>-q</arg>
			<arg>--no-outline</arg>
			<arg>${URL}</arg>
			<arg>${FILENAMEOUT}</arg>
		</pdf-scheda>
		<pdf-favourites-merge>
			<executable>/usr/bin/nice</executable>
			<arg>-n</arg>
			<arg>19</arg>
			<arg>${config/paths/basePath}/bin/cpdf</arg>
			<arg>${FILENAMEIN}</arg>
			<arg>-o</arg>
			<arg>${FILENAMEOUT}</arg>
		</pdf-favourites-merge>
		<pdf-favourites-cover>
			<!-- Su linux gli argomenti fissi sono "cablati" in uno script altrimenti non funziona -->
			<executable>${config/paths/basePath}/bin/cpdf-mypersonalizzato.sh</executable>
			<arg>${TITLESUBTITLE}</arg>
 			<arg>${FILENAMEIN}</arg>
			<arg>${FILENAMEOUT}</arg>
		</pdf-favourites-cover>
		<!-- Comando che ritaglia un'immagine specificando esattamente la posizione e la dimensione del ritaglio, e poi riducendo alla dimensione finale. -->
		<yadaCropAndResize-old>
			<executable>convert</executable>
			<arg>-auto-orient</arg>
			<arg>${FILENAMEIN}</arg>
			<arg>-background</arg> <!-- "-background white -flatten" converts any transparent png backround to white instead of the default black -->
			<arg>white</arg>
			<arg>-flatten</arg>
			<arg>-crop</arg>
			<arg>${w}x${h}+${x}+${y}</arg>
			<arg>-resize</arg>
			<arg>${resizew}x${resizeh}&gt;</arg>
			<arg>${FILENAMEOUT}</arg>
		</yadaCropAndResize-old>
		<!-- Questa versione gestisce le gif animate -->
		<yadaCropAndResize timeoutseconds="180">
			<executable>convert</executable>
			<arg>-auto-orient</arg>
			<arg>${FILENAMEIN}</arg>
			<arg>-coalesce</arg>
			<arg>-repage</arg>
			<arg>0x0</arg>
			<arg>-crop</arg>
			<arg>${w}x${h}+${x}+${y}</arg>
			<arg>-resize</arg>
			<arg>${resizew}x${resizeh}&gt;</arg>
			<arg>+repage</arg>
			<arg>${FILENAMEOUT}</arg>
		</yadaCropAndResize>
		<product-gallery-migration timeoutseconds="6000">
			<executable>bash</executable> <!-- Using WSL on Windows - maybe git bash works too -->
			<arg>${config/paths/basePath}/bin/checksumProductFileMigration.sh</arg>
		</product-gallery-migration>
		
	</shell>
</config>