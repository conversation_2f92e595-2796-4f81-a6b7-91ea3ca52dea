## Aggiunta titoli e sottotitoli in versione Mobile del Project e della News per la HomePage multilingua

create table HomeNewsTitleMobile (id bigint not null auto_increment, localeCode varchar(5), value longtext, HomePage_id bigint, primary key (id)) engine=InnoDB;
create table HomeProjectTitleMobile (id bigint not null auto_increment, localeCode varchar(5), value longtext, HomePage_id bigint, primary key (id)) engine=InnoDB;

alter table HomeNewsTitleMobile add constraint FKjsg8j8h57f5svrbp3i6of0i5s foreign key (HomePage_id) references HomePage (id);
alter table HomeProjectTitleMobile add constraint FKrwe1f06wxc3awvo17d8354iqt foreign key (HomePage_id) references HomePage (id);
