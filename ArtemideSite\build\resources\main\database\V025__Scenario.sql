## Refactoring scenario con flag su prodotto

# Aggiunta flag al prodotto
alter table Prodotto add column scenarios bit not null;

# NO Rimozione dalla serie della sottofamiglia
# NO update Subfamily set series="" where series = "9";
# NO update Subfamily set series=REPLACE(series, ",9", "") where series like "%,9";

# Set del flag sui prodotti indicati nel file xls
update Prodotto p join Prodotto_Componente pc on pc.Prodotto_id = p.id join Articolo a ON a.id = pc.componenti_id
set p.scenarios = true
where a.codiceSap in ("1729010A","1169010A","1145120A","1151010A","1152010A","1611010A","DD0089D10","DD0089D01","DD0089D09","DD0089D16","DD0089C10","DD0089C01","DD0089C09","DD0089C16","DD0089B10","DD0089B01","DD0089B09","DD0089B16","DD0089A10","DD0089A01","DD0089A09","DD0089A16","1820010A","1652010A","1653010A","1655010A","1983010A","1983020A","1984010A","1984020A","1985010A","1985020A","1986010A","1986020A","1630010A","0367010A","0319010A","0369030A","0369040A","1641010A","1900120A","1904120A","1908320A","1908350A","1902120A","1906120A","1908220A","1908250A","1650010A","NL702710K0","NL702725K0","NL840210K0","NL840225K0","1944020A","DOI4300A14","DOI4300A12","DOI4300A13","1736010A","1737010A","1738010A","DJ1001C00","DJ1001C03","DJ1001C10","DJ1001C09","DJ1001C02","DJ1001C16","DJ1001G00","DJ1001G03","DJ1001G10","DJ1001G09","DJ1001G02","DJ1001G16","DJ1001L00","DJ1001L03","DJ1001L10","DJ1001L09","DJ1001L02","DJ1001L16","DJ1001A00","DJ1001A03","DJ1001A10","DJ1001A09","DJ1001A02","DJ1001A16","DJ1001O02","DJ1001P02","1295010A","1296010A","1708010A","0940010A","0940020A","0940050A","0940080A","0941010A","0941020A","0941050A","0941080A","C225410","C225440","C225510","C225540","C234010","C234040","1979020A","T087300","NL1760010K006","NL1760010K002","NL1760010K004","1639010A","1728010A","DAL0028A00","DAL0028A14","DAL0028A16","0688510A","0677510A","1631040A","DD0091C21","DD0091B21","1940030A")
