package com.artemide.core;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.annotation.Order;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.web.SecurityFilterChain;

import net.yadaframework.security.YadaSecurityConfig;

/**
 * Security configuration per la sezione "Journal".
 */
@Deprecated // Messo come esperimento ma non funziona perché non blocca "/documenti/journal/**"
@Configuration
@Order(7)
@EnableWebSecurity
//This has been disabled in WebApplicationInitializer
public class NewsJournalSecurityConfig extends YadaSecurityConfig {

	public NewsJournalSecurityConfig() {
		loginUrl = "/journal/login";
		loginUrlAjax = "/journal/login";
	}

	@Bean
	public SecurityFilterChain newsJournalSecurityConfigChain(HttpSecurity http) throws Exception {
		// super.failureHandler.setFailureUrlNormalRequest("/journal/login");
		// super.failureHandler.setFailureUrlAjaxRequest("/journal/login");
		// super.successHandler.setTargetUrlParameter("someParameter");
		super.successHandler.setDefaultTargetUrlNormalRequest("/journal");
		super.successHandler.setDefaultTargetUrlAjaxRequest("/yadaLoginSuccess"); // No targetUrl on ajax login
		super.logoutSuccessHandler.setDefaultTargetUrl("/journal"); // language path will be added in the handler

		//	The mapping matches URLs using the following rules:
		//		? matches one character
		//		* matches zero or more characters
		//		** matches zero or more 'directories' in a path
		//	Patterns which end with /** (and have no other wildcards) are optimized by using a substring match
		http
				.authorizeHttpRequests(authorize -> authorize
						// Apply specific rules for paths under "/journal"
						.requestMatchers("/documenti/journal/**").authenticated()
						.requestMatchers("/journal/**").permitAll())
				.formLogin(form -> form
						// Configure login page and processing URL for "/journal"
						.loginPage("/journal/login")
						.loginProcessingUrl("/journal/loginPost"))
				.logout(logout -> logout
						// Configure logout URL and success handler for "/journal"
						.logoutUrl("/journal/logout")
						.logoutSuccessHandler(logoutSuccessHandler));
		super.configure(http);
		return http.build();
    }
}
