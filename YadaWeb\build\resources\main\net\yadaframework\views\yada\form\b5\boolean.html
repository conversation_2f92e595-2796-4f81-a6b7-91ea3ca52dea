<!DOCTYPE html>
<html xmlns:yada="http://www.yadaframework.net" xmlns:th="http://www.thymeleaf.org">
<head>
<meta charset="UTF-8">
</head>
<!--/*
A checkbox for boolean elements.

Parameters:
- fieldName = name of the field holding the value
- id = id of the field holding the value (optional). Please note that even if not set it could take the value of an enclosing modalGeneric
- switch = true to render as a switch, otherwise render as a standard checkbox
- labelKey = key in messages.properties for the label text
- label = label to use, if not using a labelKey
- help = testo per il pulsante di help, se non c'Ã¨ helpKey (optional)
- helpKey = chiave del testo per il pulsante di help (optional)
- disabled = true for a disabled checkbox (optional)
- toggle = css selector of an element to toggle (optional)

Example: <div th:replace="/yada/form/b5/boolean::field(fieldName='useGold',label='Use Gold',help='Enable the use of gold to complete building')"></div>
Note: you can't set a default value using thymeleaf, must do it in the controller.

- NO: help = testo per il pulsante di help (optional) Can be localised with: help=#{form.help.notifyCommentMyStory}

*/-->
<body th:remove="tag">
<!--/* The default id (inputId) is a sequence derived from the current URI, so that ajax-loaded modals get a different id from the
	 * main page. This doesn't work when many page elements are ajax-loaded from the same url of course, in which
	 * case you need to pass a specific id.
	 * Had to check for isFragment because id is never null when the form is inside a modalGeneric.
	 */--> 
<div class="form-group" 
	th:with="hasError=${#fields.hasErrors('__${fieldName}__')}, 
		inputId=${id==null || @yadaWebUtil.isFragment(id) ? #ids.seq(@yadaWebUtil.getRequestMapping()) : id}">
	<div class="checkbox">
		<label th:if="${switch==null or switch!=true}" class="control-label" th:id="${inputId}+'label'">
			<input type="checkbox" th:field="*{__${fieldName}__}" th:required="${required}" th:disabled="${disabled}" 
				th:classappend="${hasError}? is-invalid"
				th:id="${inputId}" th:attr="aria-describedby=${inputId}+'label'"/>
			<span th:utext="${labelKey!=null?#messages.msg(labelKey):label}">Subscribe</span> 
		</label>
		<th:block th:if="${switch==true}">
			<span> <!--/* Span to protect the outside label */-->
				<input type="checkbox" th:field="*{__${fieldName}__}" th:disabled="${disabled}" 
					th:classappend="${hasError}? is-invalid"
					class="yadaSwitch yadaSwitch-round" th:id="${inputId}"/>
				<label th:for="${inputId}"></label>
			</span>
			<label class="yadaSwitchLabel" th:for="${inputId}" th:utext="${labelKey!=null?#messages.msg(labelKey):label}" >
				Subscribe
			</label>
		</th:block>
	
		<th:block th:with="help = ${helpKey!=null}?#{__${helpKey}__}:${help}">
			<a th:if="${help!=null}" class="yadaHelpButton" data-trigger="focus" data-container="body" 
				data-toggle="popover" data-placement="auto bottom" tabindex="-1"
				data-html="true"
				th:attr="data-content=${help}">
				<i class="yadaIcon yadaIcon-help"></i>
			</a>
		</th:block>
	</div>
	
	<script th:if="${toggle!=null}" type="text/javascript" th:inline="javascript">
          $([[${'#'+inputId}]]).change(function() {
        	  if (this.checked) {
        		  $([[${toggle}]]).slideDown();
        	  } else {
        		  $([[${toggle}]]).slideUp();
        	  }
          });
	</script>         
	
	<div th:each="err : ${#fields.errors('__${fieldName}__')}" class="alert alert-danger alert-dismissible yadaInputError" role="alert">
		<span th:text="${err}">Error Text</span>
		<button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
	</div>
	
</div>    	
</body>
</html>
