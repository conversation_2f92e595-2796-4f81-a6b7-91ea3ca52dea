create table Publication (hiddenBrochure bit not null, pos integer not null, typology integer, coverImage_id bigint, docFileDe_id bigint, docFileEn_id bigint, docFileEs_id bigint, docFileFr_id bigint, docFileIt_id bigint, id bigint not null auto_increment, version bigint not null, primary key (id)) engine=InnoDB;
create table Publication_link (Publication_id bigint not null, locale varchar(32) not null, link varchar(255), primary key (Publication_id, locale)) engine=InnoDB;
create table Publication_Subfamily (Publication_id bigint not null, Subfamily_id bigint not null) engine=InnoDB;
create table Publication_subtitle (Publication_id bigint not null, locale varchar(32) not null, subtitle varchar(255), primary key (Publication_id, locale)) engine=InnoDB;
create table Publication_title (Publication_id bigint not null, locale varchar(32) not null, title varchar(255), primary key (Publication_id, locale)) engine=InnoDB;

alter table Publication add constraint UK_lvipxabuxr6gspm9q1t4tt3wv unique (coverImage_id);
alter table Publication add constraint UK_oxfsqmnfgc8ss8kb2lvmwloy7 unique (docFileDe_id);
alter table Publication add constraint UK_iu7bsj5o0mv8ntvr89cw0xgif unique (docFileEn_id);
alter table Publication add constraint UK_smg0gk7eeelr79vto6vu08m2n unique (docFileEs_id);
alter table Publication add constraint UK_aije6n3ju7h4nqlfx3fy50jxx unique (docFileFr_id);
alter table Publication add constraint UK_c89s5q8nmxhljf34huplqnh38 unique (docFileIt_id);
alter table Publication_Subfamily add constraint UK1gw2xbidlqj9uncbv1rd3dbob unique (Publication_id, Subfamily_id);

alter table Publication add constraint FKnd04gn49chmxf9lhu8c2n3ul3 foreign key (coverImage_id) references YadaAttachedFile (id);
alter table Publication add constraint FK9blxk4rxahxil6dctbhl9a7gv foreign key (docFileDe_id) references YadaAttachedFile (id);
alter table Publication add constraint FKdlcblfpr3lduqgw6t2qfg3ruf foreign key (docFileEn_id) references YadaAttachedFile (id);
alter table Publication add constraint FKa8xydjimux7ymagdcc6jpkkc1 foreign key (docFileEs_id) references YadaAttachedFile (id);
alter table Publication add constraint FKoxn0d1fis4tn5ngx4txppjsta foreign key (docFileFr_id) references YadaAttachedFile (id);
alter table Publication add constraint FK6b5wslxmpjtraefu0utp0penp foreign key (docFileIt_id) references YadaAttachedFile (id);
alter table Publication_link add constraint FKbadd4qvg4rjttyecxxcwhgbu5 foreign key (Publication_id) references Publication (id);
alter table Publication_Subfamily add constraint FKct0mjwladjb7jmrhisc4j27u6 foreign key (Subfamily_id) references Subfamily (id);
alter table Publication_Subfamily add constraint FKnp509l7b0i4hh14cjcvqqfs6p foreign key (Publication_id) references Publication (id);
alter table Publication_subtitle add constraint FKp0ll95vuvdoitemeduxe9g2wk foreign key (Publication_id) references Publication (id);
alter table Publication_title add constraint FK7s2ysxlj8dx88w283r3ubdbko foreign key (Publication_id) references Publication (id);
