## Creazioni tabelle per il MicroBanner presente sulla HomePage
## Aggiunta colonna enableMicroBanner per HomePage

create table HomeLongBannerExtraLine (id bigint not null auto_increment, localeCode varchar(5), value longtext, HomePage_id bigint, primary key (id)) engine=InnoDB;
create table HomeLongBannerLink (id bigint not null auto_increment, localeCode varchar(5), value longtext, HomePage_id bigint, primary key (id)) engine=InnoDB;
create table HomeLongBannerSubtitle (id bigint not null auto_increment, localeCode varchar(5), value longtext, HomePage_id bigint, primary key (id)) engine=InnoDB;
create table HomeLongBannerTitle (id bigint not null auto_increment, localeCode varchar(5), value longtext, HomePage_id bigint, primary key (id)) engine=InnoDB;
create table HomeMicroBannerLink (id bigint not null auto_increment, localeCode varchar(5), value longtext, HomePage_id bigint, primary key (id)) engine=InnoDB;
create table HomeMicroBannerTitle (id bigint not null auto_increment, localeCode varchar(5), value longtext, HomePage_id bigint, primary key (id)) engine=InnoDB;
ALTER TABLE HomePage ADD COLUMN enableMicroBanner bit not null AFTER fourthMostViewed;