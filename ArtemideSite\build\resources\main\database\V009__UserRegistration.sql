alter table YadaUserProfile add column locale varchar(32);
alter table YadaPersistentEnum MODIFY enumClassName varchar(191);
alter table YadaPersistentEnum_langToText MODIFY localText varchar(128);
alter table YadaPersistentEnum_langToText MODIFY language varchar(32);

create table YadaUserProfile_Prodotto (UserProfile_id bigint not null, favouriteProducts_id bigint not null) engine=InnoDB;
alter table YadaUserProfile_Prodotto add constraint FKatjvc639y2f7fr3715wibv6mc foreign key (favouriteProducts_id) references Prodotto (id);
alter table YadaUserProfile_Prodotto add constraint FK8fopvhs421o4w4j9ishhh7ur0 foreign key (UserProfile_id) references YadaUserProfile (id);

alter table YadaUserProfile_Famiglia add constraint UK498gf6vl8y9m64m1imoxxqjfr unique (UserProfile_id, favouriteFamilies_id);
alter table YadaUserProfile_Prodotto add constraint UKk2jwockof622fe3prwuiv702t unique (UserProfile_id, favouriteProducts_id);
alter table YadaUserProfile_Project add constraint UKm0lyjwug5bhnbxfdsmghbrqsq unique (UserProfile_id, favouriteProjects_id);
alter table YadaUserProfile_Subfamily add constraint UKglbkv7dpxbif4x3mvdgvbkpww unique (UserProfile_id, favouriteSubfamilies_id);
