# Home builder classes

ALTER TABLE PageModule
ADD COLUMN enabled bit not null after id,
ADD COLUMN homepage_id bigint after pdfFile_id;

create table HomePage2 (id bigint not null auto_increment, backgroundColorMircobanner varchar(255), description varchar(255), enableMicroBanner bit not null, enabled bit not null, expirationDateMicroBanner datetime, textColorMicroBanner varchar(255), version bigint not null, primary key (id)) engine=InnoDB;
create table HomePage2_linksMicroBanner (HomePage2_id bigint not null, linksMicroBanner varchar(255), locale varchar(32) not null, primary key (HomePage2_id, locale)) engine=InnoDB;
create table HomePage2_titlesMicroBanner (HomePage2_id bigint not null, titlesMicroBanner varchar(255), locale varchar(32) not null, primary key (HomePage2_id, locale)) engine=InnoDB;

alter table HomePage2_linksMicroBanner add constraint FKn99gck3ar4igis5vufq9vi43i foreign key (HomePage2_id) references HomePage2 (id);
alter table HomePage2_titlesMicroBanner add constraint FKtc080tnp6c7qa12whe4cg6m1n foreign key (HomePage2_id) references HomePage2 (id);
alter table PageModule add constraint FK9ca0tos3a4s5a917770loslh0 foreign key (homepage_id) references HomePage2 (id);



